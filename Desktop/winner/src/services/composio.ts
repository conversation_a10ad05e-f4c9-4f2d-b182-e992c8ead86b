import { apiService } from './api';

export interface ComposioIntegration {
  id: string;
  name: string;
  description: string;
  category: string;
  status: 'connected' | 'disconnected' | 'error';
  config: Record<string, any>;
  last_sync?: string;
}

export interface ComposioAction {
  id: string;
  integration: string;
  action: string;
  parameters: Record<string, any>;
  result?: any;
  status: 'pending' | 'running' | 'completed' | 'failed';
}

class ComposioService {
  async getIntegrations(): Promise<ComposioIntegration[]> {
    try {
      const response = await apiService.get('/api/v1/integrations');
      return response.integrations;
    } catch (error) {
      console.error('Error getting integrations:', error);
      throw error;
    }
  }

  async connectIntegration(integrationId: string, config: Record<string, any>): Promise<ComposioIntegration> {
    try {
      const response = await apiService.post(`/api/v1/integrations/${integrationId}/connect`, {
        config
      });
      return response.integration;
    } catch (error) {
      console.error(`Error connecting integration ${integrationId}:`, error);
      throw error;
    }
  }

  async disconnectIntegration(integrationId: string): Promise<void> {
    try {
      await apiService.post(`/api/v1/integrations/${integrationId}/disconnect`);
    } catch (error) {
      console.error(`Error disconnecting integration ${integrationId}:`, error);
      throw error;
    }
  }

  async executeAction(integrationId: string, action: string, parameters: Record<string, any>): Promise<any> {
    try {
      const response = await apiService.post(`/api/v1/integrations/${integrationId}/execute`, {
        action,
        parameters
      });
      return response;
    } catch (error) {
      console.error(`Error executing action ${action} on ${integrationId}:`, error);
      throw error;
    }
  }

  async syncIntegration(integrationId: string): Promise<void> {
    try {
      await apiService.post(`/api/v1/integrations/${integrationId}/sync`);
    } catch (error) {
      console.error(`Error syncing integration ${integrationId}:`, error);
      throw error;
    }
  }

  async getIntegration(integrationId: string): Promise<ComposioIntegration> {
    try {
      return await apiService.get(`/api/v1/integrations/${integrationId}`);
    } catch (error) {
      console.error(`Error getting integration ${integrationId}:`, error);
      throw error;
    }
  }
}

export const composio = new ComposioService();