import { useState, useCallback } from 'react';
import { aiOrchestrator, AgentSuggestion } from '../services/ai-orchestrator';
import { CrewResult } from '../services/crewai';
import { LiveKitSession } from '../services/livekit';
import toast from 'react-hot-toast';

export interface ChatMessage {
  id: string;
  agent: string;
  content: string;
  timestamp: Date;
  type: 'user' | 'agent';
  suggestions?: AgentSuggestion[];
}

export const useAIOrchestrator = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [pendingSuggestions, setPendingSuggestions] = useState<AgentSuggestion[]>([]);
  const [liveKitSession, setLiveKitSession] = useState<LiveKitSession | null>(null);

  const sendMessage = useCallback(async (content: string, selectedAgents?: string[]) => {
    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      agent: 'user',
      content,
      timestamp: new Date(),
      type: 'user'
    };

    setMessages(prev => [...prev, userMessage]);
    setIsProcessing(true);

    try {
      // Process with AI orchestrator
      const { responses, suggestions } = await aiOrchestrator.processUserInput(content, selectedAgents);

      // Convert responses to chat messages
      const agentMessages: ChatMessage[] = responses.map((response, index) => ({
        id: (Date.now() + index).toString(),
        agent: getAgentName(response.agent_id),
        content: response.result,
        timestamp: new Date(),
        type: 'agent' as const,
        suggestions: index === 0 ? suggestions.filter(s => s.agentId === response.agent_id) : undefined
      }));

      setMessages(prev => [...prev, ...agentMessages]);
      setPendingSuggestions(suggestions);

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to process message');
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const executeSuggestion = useCallback(async (suggestion: AgentSuggestion) => {
    // Add user acceptance message
    const acceptMessage: ChatMessage = {
      id: Date.now().toString(),
      agent: 'user',
      content: `Yes, let's proceed with: ${suggestion.title}`,
      timestamp: new Date(),
      type: 'user'
    };

    setMessages(prev => [...prev, acceptMessage]);
    setIsProcessing(true);

    try {
      const result = await aiOrchestrator.executeSuggestion(suggestion);

      // Add agent response
      const responseMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        agent: suggestion.agentName,
        content: `Perfect! I've completed the ${suggestion.title.toLowerCase()}. ${result.result}`,
        timestamp: new Date(),
        type: 'agent'
      };

      setMessages(prev => [...prev, responseMessage]);
      
      // Remove executed suggestion
      setPendingSuggestions(prev => prev.filter(s => s.id !== suggestion.id));

    } catch (error) {
      console.error('Error executing suggestion:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        agent: suggestion.agentName,
        content: `I encountered an issue while executing ${suggestion.title}. Please try again.`,
        timestamp: new Date(),
        type: 'agent'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const startLiveKitSession = useCallback(async (agentIds: string[], sessionType: 'voice' | 'video' | 'screen-share') => {
    try {
      const session = await aiOrchestrator.startLiveKitSession(agentIds, sessionType);
      setLiveKitSession(session);
      return session;
    } catch (error) {
      console.error('Error starting LiveKit session:', error);
      throw error;
    }
  }, []);

  const endLiveKitSession = useCallback(async () => {
    if (liveKitSession) {
      try {
        await aiOrchestrator.endLiveKitSession(liveKitSession.id);
        setLiveKitSession(null);
      } catch (error) {
        console.error('Error ending LiveKit session:', error);
        throw error;
      }
    }
  }, [liveKitSession]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setPendingSuggestions([]);
  }, []);

  return {
    messages,
    isProcessing,
    pendingSuggestions,
    liveKitSession,
    sendMessage,
    executeSuggestion,
    startLiveKitSession,
    endLiveKitSession,
    clearMessages
  };
};

function getAgentName(agentId: string): string {
  const agentNames: Record<string, string> = {
    strategic: 'Strategic',
    product: 'Product',
    technical: 'Technical',
    operations: 'Operations',
    marketing: 'Marketing'
  };
  return agentNames[agentId] || agentId;
}