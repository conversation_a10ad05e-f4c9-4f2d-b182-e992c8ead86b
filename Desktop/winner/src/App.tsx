import React, { useEffect, useState } from 'react';
import { Toaster } from 'react-hot-toast';
import { AuthProvider, useAuth } from './components/AuthProvider';
import { AuthPage } from './components/AuthPage';
import { Homepage } from './components/Homepage';
import { Dashboard } from './components/Dashboard';
import { SystemHealthCheck } from './components/SystemHealthCheck';
import { apiService } from './services/api';
import { Loader2 } from 'lucide-react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Documentation } from './pages/Documentation';
import { DataRoom } from './pages/DataRoom';
import { API } from './pages/API';
import { Integrations } from './pages/Integrations';
import { Company } from './pages/Company';
import { Pricing } from './pages/Pricing';
import { About } from './pages/About';
import { Contact } from './pages/Contact';
import { Careers } from './pages/Careers';
import { Settings } from './pages/Settings';

const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const [showAuthPage, setShowAuthPage] = useState(false);
  const [isCheckingHealth, setIsCheckingHealth] = useState(true);

  useEffect(() => {
    checkBackendHealth();
  }, []);

  const checkBackendHealth = async () => {
    try {
      await apiService.healthCheck();
      console.log('✅ Backend is healthy');
    } catch (error) {
      console.warn('⚠️ Backend health check failed:', error);
    } finally {
      setIsCheckingHealth(false);
    }
  };

  // Check if URL has health-check parameter
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('health-check')) {
      window.location.href = '/health-check';
    }
  }, []);

  if (isLoading || isCheckingHealth) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-purple-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading Ultimate Co-founder...</p>
        </div>
      </div>
    );
  }

  if (showAuthPage) {
    return <AuthPage onClose={() => setShowAuthPage(false)} />;
  }

  return (
    <Router>
      <Routes>
        <Route path="/" element={<Homepage onShowAuth={() => setShowAuthPage(true)} />} />
        <Route 
          path="/dashboard/*" 
          element={isAuthenticated ? <Dashboard /> : <Navigate to="/" />} 
        />
        <Route path="/health-check" element={<SystemHealthCheck />} />
        <Route path="/documentation" element={<Documentation />} />
        <Route path="/data-room" element={<DataRoom />} />
        <Route path="/api" element={<API />} />
        <Route path="/integrations" element={<Integrations />} />
        <Route path="/company" element={<Company />} />
        <Route path="/pricing" element={<Pricing />} />
        <Route path="/about" element={<About />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/careers" element={<Careers />} />
        <Route path="/settings" element={<Settings />} />
      </Routes>
    </Router>
  );
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
      <Toaster 
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
    </AuthProvider>
  );
}

export default App;