import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useAgentStore } from '../store/agentStore';
import { AgentCard } from './AgentCard';
import { ChatPanel } from './ChatPanel';
import { VideoCallPanel } from './VideoCallPanel';
import { IntegrationsPanel } from './IntegrationsPanel';
import { CodeGenerationPanel } from './CodeGenerationPanel';
import { StrategicAgent } from './StrategicAgent';
import { ProductAgent } from './ProductAgent';
import { TechnicalAgent } from './TechnicalAgent';
import { OperationsAgent } from './OperationsAgent';
import { MarketingAgent } from './MarketingAgent';
import { StartupOrchestrator } from './StartupOrchestrator';
import { BoltIntegrationAgent } from './BoltIntegrationAgent';
import { TimelineAgent } from './TimelineAgent';
import { MonetizationAgent } from './MonetizationAgent';
import { PitchAgent } from './PitchAgent';
import { DataRoomAgent } from './DataRoomAgent';
import { Routes, Route, Link, useNavigate, useLocation } from 'react-router-dom';
import { 
  Users2, 
  MessageSquare, 
  Video, 
  Code2, 
  Zap, 
  Settings,
  Bell,
  Moon,
  Sun,
  TrendingUp,
  Rocket,
  Wrench,
  Briefcase,
  Megaphone,
  Crown,
  ExternalLink,
  Calendar,
  DollarSign,
  Presentation,
  FolderOpen,
  FileText,
  Globe,
  Phone,
  Mail,
  Briefcase as BriefcaseIcon,
  LogOut
} from 'lucide-react';
import { useAuth } from './AuthProvider';
import { SettingsPanel } from './SettingsPanel';

export const Dashboard: React.FC = () => {
  const { 
    agents, 
    selectedAgent, 
    setSelectedAgent, 
    isVideoCallActive, 
    toggleVideoCall 
  } = useAgentStore();

  const [activePanel, setActivePanel] = useState<'chat' | 'video' | 'integrations' | 'code' | 'settings'>('chat');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const { logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  React.useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDarkMode]);

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <Link to="/dashboard" className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center hover:scale-105 transition-transform">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  Ultimate Co-founder
                </h1>
              </Link>
            </div>

            <div className="hidden md:flex items-center space-x-6">
              <Link to="/documentation" className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">
                Documentation
              </Link>
              <Link to="/data-room" className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">
                Data Room
              </Link>
              <Link to="/api" className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">
                API
              </Link>
              <Link to="/integrations" className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">
                Integrations
              </Link>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                {agents.filter(a => a.status === 'active').length > 0 && (
                  <div className="flex items-center gap-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    {agents.filter(a => a.status === 'active').length} Active
                  </div>
                )}
              </div>

              <button
                onClick={() => setIsDarkMode(!isDarkMode)}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
              </button>

              <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors relative">
                <Bell size={20} />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full" />
              </button>

              <Link to="/settings" className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
                <Settings size={20} />
              </Link>

              <button 
                onClick={handleLogout}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <LogOut size={20} />
              </button>
            </div>
          </div>
        </div>
      </header>

      <Routes>
        <Route path="/" element={
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Agents Grid */}
              <div className="lg:col-span-2">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Your AI Co-founders
                  </h2>
                  <div className="flex items-center gap-2">
                    <Users2 className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {agents.length} Agents
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {agents.map((agent) => (
                    <motion.div
                      key={agent.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: agents.indexOf(agent) * 0.1 }}
                    >
                      <AgentCard
                        agent={agent}
                        onSelect={setSelectedAgent}
                        isSelected={selectedAgent?.id === agent.id}
                      />
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Right Panel */}
              <div className="lg:col-span-1">
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 h-fit">
                  {/* Panel Tabs */}
                  <div className="border-b border-gray-200 dark:border-gray-700">
                    <nav className="flex flex-wrap">
                      <button
                        onClick={() => setActivePanel('chat')}
                        className={`flex-1 flex items-center justify-center gap-1 px-1 py-3 text-xs font-medium transition-colors min-w-0 ${
                          activePanel === 'chat'
                            ? 'text-purple-600 border-b-2 border-purple-600'
                            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                        }`}
                      >
                        <MessageSquare size={12} />
                        <span className="hidden sm:inline truncate">Chat</span>
                      </button>
                      <button
                        onClick={() => setActivePanel('video')}
                        className={`flex-1 flex items-center justify-center gap-1 px-1 py-3 text-xs font-medium transition-colors min-w-0 ${
                          activePanel === 'video'
                            ? 'text-purple-600 border-b-2 border-purple-600'
                            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                        }`}
                      >
                        <Video size={12} />
                        <span className="hidden sm:inline truncate">Video</span>
                      </button>
                      <button
                        onClick={() => setActivePanel('code')}
                        className={`flex-1 flex items-center justify-center gap-1 px-1 py-3 text-xs font-medium transition-colors min-w-0 ${
                          activePanel === 'code'
                            ? 'text-purple-600 border-b-2 border-purple-600'
                            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                        }`}
                      >
                        <Code2 size={12} />
                        <span className="hidden sm:inline truncate">Code</span>
                      </button>
                      <button
                        onClick={() => setActivePanel('integrations')}
                        className={`flex-1 flex items-center justify-center gap-1 px-1 py-3 text-xs font-medium transition-colors min-w-0 ${
                          activePanel === 'integrations'
                            ? 'text-purple-600 border-b-2 border-purple-600'
                            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                        }`}
                      >
                        <Zap size={12} />
                        <span className="hidden sm:inline truncate">Apps</span>
                      </button>
                      <button
                        onClick={() => setActivePanel('settings')}
                        className={`flex-1 flex items-center justify-center gap-1 px-1 py-3 text-xs font-medium transition-colors min-w-0 ${
                          activePanel === 'settings'
                            ? 'text-purple-600 border-b-2 border-purple-600'
                            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                        }`}
                      >
                        <Settings size={12} />
                        <span className="hidden sm:inline truncate">Settings</span>
                      </button>
                    </nav>
                  </div>

                  {/* Panel Content */}
                  <div className="p-6">
                    {activePanel === 'chat' && <ChatPanel />}
                    {activePanel === 'video' && <VideoCallPanel />}
                    {activePanel === 'code' && <CodeGenerationPanel />}
                    {activePanel === 'integrations' && <IntegrationsPanel />}
                    {activePanel === 'settings' && <SettingsPanel />}
                  </div>
                </div>
              </div>
            </div>
          </div>
        } />
        <Route path="/strategic" element={<StrategicAgent />} />
        <Route path="/product" element={<ProductAgent />} />
        <Route path="/technical" element={<TechnicalAgent />} />
        <Route path="/operations" element={<OperationsAgent />} />
        <Route path="/marketing" element={<MarketingAgent />} />
        <Route path="/orchestrator" element={<StartupOrchestrator />} />
        <Route path="/bolt" element={<BoltIntegrationAgent />} />
        <Route path="/timeline" element={<TimelineAgent />} />
        <Route path="/monetization" element={<MonetizationAgent />} />
        <Route path="/pitch" element={<PitchAgent />} />
        <Route path="/dataroom" element={<DataRoomAgent />} />
      </Routes>
    </div>
  );
};