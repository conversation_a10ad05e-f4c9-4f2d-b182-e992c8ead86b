import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Code2, 
  Database, 
  Server, 
  Cloud, 
  Monitor,
  Share2,
  Download,
  FileText,
  Play,
  Pause,
  SkipForward,
  CheckCircle,
  AlertTriangle,
  Zap,
  GitBranch,
  Package,
  Settings
} from 'lucide-react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface TechStack {
  name: string;
  description: string;
  technologies: string[];
  pros: string[];
  cons: string[];
  complexity: 'Low' | 'Medium' | 'High';
  timeToMVP: string;
  scalability: number;
  cost: 'Low' | 'Medium' | 'High';
}

interface ArchitectureComponent {
  name: string;
  type: 'client' | 'api' | 'service' | 'database' | 'external';
  description: string;
  connections: string[];
}

interface ScaffoldFile {
  path: string;
  content: string;
  type: 'config' | 'code' | 'docs' | 'test';
}

interface TestCase {
  name: string;
  description: string;
  type: 'unit' | 'integration' | 'e2e';
  priority: 'high' | 'medium' | 'low';
  testCode: string;
}

interface LiveKitDemo {
  url: string;
  isActive: boolean;
  duration: number;
  timestamps: Array<{
    time: string;
    description: string;
  }>;
}

export const TechnicalAgent: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<'intro' | 'stacks' | 'architecture' | 'scaffold' | 'demo' | 'tests' | 'complete'>('intro');
  const [selectedStack, setSelectedStack] = useState<TechStack | null>(null);
  const [techStacks, setTechStacks] = useState<TechStack[]>([]);
  const [architecture, setArchitecture] = useState<ArchitectureComponent[]>([]);
  const [scaffoldFiles, setScaffoldFiles] = useState<ScaffoldFile[]>([]);
  const [testCases, setTestCases] = useState<TestCase[]>([]);
  const [demoSession, setDemoSession] = useState<LiveKitDemo>({
    url: '',
    isActive: false,
    duration: 0,
    timestamps: []
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);

  const generateTechStacks = async () => {
    setIsProcessing(true);
    setCurrentStep('stacks');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const mockStacks: TechStack[] = [
      {
        name: 'MERN Stack',
        description: 'MongoDB, Express.js, React, Node.js - Full JavaScript ecosystem',
        technologies: ['MongoDB', 'Express.js', 'React', 'Node.js', 'JWT', 'Socket.io'],
        pros: [
          'Single language (JavaScript) across full stack',
          'Large community and extensive documentation',
          'Rapid prototyping and development',
          'Rich ecosystem of npm packages',
          'Real-time capabilities with Socket.io'
        ],
        cons: [
          'JavaScript fatigue and rapid ecosystem changes',
          'MongoDB may not suit all data relationships',
          'Single-threaded Node.js limitations',
          'Potential callback hell without proper async handling'
        ],
        complexity: 'Medium',
        timeToMVP: '4-6 weeks',
        scalability: 7,
        cost: 'Medium'
      },
      {
        name: 'Django + Vue.js',
        description: 'Python Django backend with Vue.js frontend - Robust and scalable',
        technologies: ['Django', 'PostgreSQL', 'Vue.js', 'Redis', 'Celery', 'Docker'],
        pros: [
          'Django\'s "batteries included" philosophy',
          'Excellent ORM and admin interface',
          'Strong security features built-in',
          'Vue.js progressive framework approach',
          'Python\'s readability and maintainability'
        ],
        cons: [
          'Two different languages to maintain',
          'Django can be overkill for simple applications',
          'Steeper learning curve for beginners',
          'Less real-time capabilities out of the box'
        ],
        complexity: 'High',
        timeToMVP: '6-8 weeks',
        scalability: 9,
        cost: 'Medium'
      },
      {
        name: 'Serverless (AWS)',
        description: 'AWS Lambda, API Gateway, DynamoDB - Cloud-native serverless architecture',
        technologies: ['AWS Lambda', 'API Gateway', 'DynamoDB', 'S3', 'CloudFront', 'Cognito'],
        pros: [
          'Auto-scaling and pay-per-use pricing',
          'No server management required',
          'Built-in security and compliance',
          'Global CDN and edge computing',
          'Microservices architecture by design'
        ],
        cons: [
          'Vendor lock-in with AWS',
          'Cold start latency issues',
          'Complex debugging and monitoring',
          'Limited execution time and memory',
          'Potential for high costs at scale'
        ],
        complexity: 'High',
        timeToMVP: '5-7 weeks',
        scalability: 10,
        cost: 'Low'
      }
    ];

    setTechStacks(mockStacks);
    setIsProcessing(false);
  };

  const selectTechStack = (stack: TechStack) => {
    setSelectedStack(stack);
    generateArchitecture(stack);
  };

  const generateArchitecture = async (stack: TechStack) => {
    setIsProcessing(true);
    setCurrentStep('architecture');
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    let mockArchitecture: ArchitectureComponent[] = [];
    
    if (stack.name === 'MERN Stack') {
      mockArchitecture = [
        {
          name: 'React Frontend',
          type: 'client',
          description: 'Single Page Application with React Router, Redux for state management',
          connections: ['API Gateway', 'CDN']
        },
        {
          name: 'API Gateway',
          type: 'api',
          description: 'Express.js REST API with authentication middleware and rate limiting',
          connections: ['React Frontend', 'Auth Service', 'Business Logic', 'WebSocket Server']
        },
        {
          name: 'Auth Service',
          type: 'service',
          description: 'JWT-based authentication with refresh tokens and role-based access',
          connections: ['API Gateway', 'User Database']
        },
        {
          name: 'Business Logic',
          type: 'service',
          description: 'Core application logic, data validation, and business rules',
          connections: ['API Gateway', 'MongoDB', 'Redis Cache']
        },
        {
          name: 'WebSocket Server',
          type: 'service',
          description: 'Real-time communication using Socket.io for live updates',
          connections: ['API Gateway', 'Redis Cache']
        },
        {
          name: 'MongoDB',
          type: 'database',
          description: 'Primary database with collections for users, projects, and analytics',
          connections: ['Business Logic', 'Auth Service']
        },
        {
          name: 'Redis Cache',
          type: 'database',
          description: 'Session storage, caching, and pub/sub for real-time features',
          connections: ['Business Logic', 'WebSocket Server']
        },
        {
          name: 'External APIs',
          type: 'external',
          description: 'Third-party integrations (Stripe, SendGrid, Analytics)',
          connections: ['Business Logic']
        }
      ];
    } else if (stack.name === 'Django + Vue.js') {
      mockArchitecture = [
        {
          name: 'Vue.js Frontend',
          type: 'client',
          description: 'Progressive web app with Vuex state management and Vue Router',
          connections: ['Django API', 'CDN']
        },
        {
          name: 'Django API',
          type: 'api',
          description: 'Django REST Framework with token authentication and permissions',
          connections: ['Vue.js Frontend', 'Django Services', 'PostgreSQL']
        },
        {
          name: 'Django Services',
          type: 'service',
          description: 'Business logic layer with Django models, serializers, and views',
          connections: ['Django API', 'PostgreSQL', 'Redis', 'Celery Workers']
        },
        {
          name: 'Celery Workers',
          type: 'service',
          description: 'Asynchronous task processing for emails, reports, and background jobs',
          connections: ['Django Services', 'Redis', 'PostgreSQL']
        },
        {
          name: 'PostgreSQL',
          type: 'database',
          description: 'Primary relational database with ACID compliance and complex queries',
          connections: ['Django API', 'Django Services', 'Celery Workers']
        },
        {
          name: 'Redis',
          type: 'database',
          description: 'Caching, session storage, and message broker for Celery',
          connections: ['Django Services', 'Celery Workers']
        },
        {
          name: 'External Services',
          type: 'external',
          description: 'Payment processing, email delivery, and analytics integrations',
          connections: ['Django Services']
        }
      ];
    } else {
      mockArchitecture = [
        {
          name: 'CloudFront CDN',
          type: 'client',
          description: 'Global content delivery network serving static React application',
          connections: ['S3 Static Hosting', 'API Gateway']
        },
        {
          name: 'API Gateway',
          type: 'api',
          description: 'Managed API gateway with throttling, caching, and request validation',
          connections: ['CloudFront CDN', 'Lambda Functions', 'Cognito']
        },
        {
          name: 'Lambda Functions',
          type: 'service',
          description: 'Serverless compute for business logic, auto-scaling based on demand',
          connections: ['API Gateway', 'DynamoDB', 'SQS', 'External APIs']
        },
        {
          name: 'Cognito',
          type: 'service',
          description: 'Managed authentication service with user pools and identity federation',
          connections: ['API Gateway', 'Lambda Functions']
        },
        {
          name: 'DynamoDB',
          type: 'database',
          description: 'NoSQL database with automatic scaling and global tables',
          connections: ['Lambda Functions', 'DynamoDB Streams']
        },
        {
          name: 'SQS',
          type: 'service',
          description: 'Message queuing service for decoupled microservices communication',
          connections: ['Lambda Functions']
        },
        {
          name: 'S3 Static Hosting',
          type: 'database',
          description: 'Static website hosting for React frontend with versioning',
          connections: ['CloudFront CDN']
        },
        {
          name: 'External APIs',
          type: 'external',
          description: 'Third-party services integrated via Lambda functions',
          connections: ['Lambda Functions']
        }
      ];
    }

    setArchitecture(mockArchitecture);
    setIsProcessing(false);
    generateScaffold(stack);
  };

  const generateScaffold = async (stack: TechStack) => {
    setIsProcessing(true);
    setCurrentStep('scaffold');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    let mockScaffold: ScaffoldFile[] = [];
    
    if (stack.name === 'MERN Stack') {
      mockScaffold = [
        {
          path: 'README.md',
          type: 'docs',
          content: `# Restaurant Management System - MERN Stack

## Overview
A comprehensive restaurant management platform built with MongoDB, Express.js, React, and Node.js.

## Features
- Real-time reservation management
- Digital menu system
- Staff scheduling
- Analytics dashboard
- Customer feedback management

## Quick Start
\`\`\`bash
npm install
npm run dev
\`\`\`

## Architecture
- **Frontend**: React 18 with TypeScript
- **Backend**: Express.js with JWT authentication
- **Database**: MongoDB with Mongoose ODM
- **Real-time**: Socket.io for live updates

## Environment Variables
\`\`\`
MONGODB_URI=mongodb://localhost:27017/restaurant
JWT_SECRET=your-secret-key
PORT=3000
\`\`\``
        },
        {
          path: 'package.json',
          type: 'config',
          content: `{
  "name": "restaurant-management-mern",
  "version": "1.0.0",
  "description": "Restaurant management system built with MERN stack",
  "main": "server/index.js",
  "scripts": {
    "dev": "concurrently \\"npm run server\\" \\"npm run client\\"",
    "server": "nodemon server/index.js",
    "client": "cd client && npm start",
    "build": "cd client && npm run build",
    "test": "jest",
    "test:watch": "jest --watch"
  },
  "dependencies": {
    "express": "^4.18.2",
    "mongoose": "^7.5.0",
    "jsonwebtoken": "^9.0.2",
    "bcryptjs": "^2.4.3",
    "socket.io": "^4.7.2",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "nodemon": "^3.0.1",
    "concurrently": "^8.2.0",
    "jest": "^29.6.2",
    "supertest": "^6.3.3"
  }
}`
        },
        {
          path: 'server/index.js',
          type: 'code',
          content: `const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const http = require('http');
const socketIo = require('socket.io');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const reservationRoutes = require('./routes/reservations');
const menuRoutes = require('./routes/menu');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// Database connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/restaurant', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/reservations', reservationRoutes);
app.use('/api/menu', menuRoutes);

// Socket.io for real-time updates
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);
  
  socket.on('join-restaurant', (restaurantId) => {
    socket.join(restaurantId);
  });
  
  socket.on('reservation-update', (data) => {
    socket.to(data.restaurantId).emit('reservation-changed', data);
  });
  
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});`
        },
        {
          path: 'server/models/Reservation.js',
          type: 'code',
          content: `const mongoose = require('mongoose');

const reservationSchema = new mongoose.Schema({
  customerName: {
    type: String,
    required: true,
    trim: true
  },
  customerEmail: {
    type: String,
    required: true,
    lowercase: true
  },
  customerPhone: {
    type: String,
    required: true
  },
  partySize: {
    type: Number,
    required: true,
    min: 1,
    max: 20
  },
  reservationDate: {
    type: Date,
    required: true
  },
  reservationTime: {
    type: String,
    required: true
  },
  tableNumber: {
    type: Number,
    required: true
  },
  status: {
    type: String,
    enum: ['confirmed', 'seated', 'completed', 'cancelled'],
    default: 'confirmed'
  },
  specialRequests: {
    type: String,
    trim: true
  },
  restaurantId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Restaurant',
    required: true
  }
}, {
  timestamps: true
});

// Index for efficient queries
reservationSchema.index({ restaurantId: 1, reservationDate: 1 });
reservationSchema.index({ customerEmail: 1 });

module.exports = mongoose.model('Reservation', reservationSchema);`
        },
        {
          path: 'client/src/components/ReservationForm.jsx',
          type: 'code',
          content: `import React, { useState } from 'react';
import { Calendar, Clock, Users, Phone, Mail } from 'lucide-react';

const ReservationForm = ({ onSubmit, availableTimes }) => {
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    partySize: 2,
    reservationDate: '',
    reservationTime: '',
    specialRequests: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 bg-white p-6 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Make a Reservation</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Users className="inline w-4 h-4 mr-1" />
            Full Name
          </label>
          <input
            type="text"
            name="customerName"
            value={formData.customerName}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Mail className="inline w-4 h-4 mr-1" />
            Email
          </label>
          <input
            type="email"
            name="customerEmail"
            value={formData.customerEmail}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Phone className="inline w-4 h-4 mr-1" />
            Phone
          </label>
          <input
            type="tel"
            name="customerPhone"
            value={formData.customerPhone}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Calendar className="inline w-4 h-4 mr-1" />
            Date
          </label>
          <input
            type="date"
            name="reservationDate"
            value={formData.reservationDate}
            onChange={handleChange}
            required
            min={new Date().toISOString().split('T')[0]}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Clock className="inline w-4 h-4 mr-1" />
            Time
          </label>
          <select
            name="reservationTime"
            value={formData.reservationTime}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select time</option>
            {availableTimes.map(time => (
              <option key={time} value={time}>{time}</option>
            ))}
          </select>
        </div>
      </div>

      <button
        type="submit"
        className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Confirm Reservation
      </button>
    </form>
  );
};

export default ReservationForm;`
        }
      ];
    }

    setScaffoldFiles(mockScaffold);
    setIsProcessing(false);
    startDemo();
  };

  const startDemo = () => {
    setCurrentStep('demo');
    setIsScreenSharing(true);
    setDemoSession({
      url: 'wss://technical-cofounder.livekit.cloud',
      isActive: true,
      duration: 0,
      timestamps: [
        { time: '0:00', description: 'Introduction and project overview' },
        { time: '0:30', description: 'Tech stack explanation and rationale' },
        { time: '1:15', description: 'System architecture walkthrough' },
        { time: '2:00', description: 'Code scaffold demonstration' },
        { time: '3:30', description: 'Key files and folder structure' },
        { time: '4:15', description: 'Next steps and deployment strategy' }
      ]
    });

    // Simulate 5-minute demo timer
    const timer = setInterval(() => {
      setDemoSession(prev => ({
        ...prev,
        duration: prev.duration + 1
      }));
    }, 1000);

    // Auto-end demo after 5 minutes
    setTimeout(() => {
      clearInterval(timer);
      endDemo();
    }, 300000);
  };

  const endDemo = () => {
    setIsScreenSharing(false);
    setDemoSession(prev => ({ ...prev, isActive: false }));
    generateTests();
  };

  const generateTests = async () => {
    setIsProcessing(true);
    setCurrentStep('tests');
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const mockTests: TestCase[] = [
      {
        name: 'User Authentication Flow',
        description: 'Test user registration, login, and JWT token validation',
        type: 'integration',
        priority: 'high',
        testCode: `describe('Authentication', () => {
  test('should register new user successfully', async () => {
    const userData = {
      name: 'John Doe',
      email: '<EMAIL>',
      password: 'password123'
    };
    
    const response = await request(app)
      .post('/api/auth/register')
      .send(userData)
      .expect(201);
    
    expect(response.body).toHaveProperty('token');
    expect(response.body.user.email).toBe(userData.email);
  });
  
  test('should login with valid credentials', async () => {
    const response = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      })
      .expect(200);
    
    expect(response.body).toHaveProperty('token');
  });
});`
      },
      {
        name: 'Reservation CRUD Operations',
        description: 'Test creating, reading, updating, and deleting reservations',
        type: 'integration',
        priority: 'high',
        testCode: `describe('Reservations', () => {
  let authToken;
  
  beforeEach(async () => {
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password' });
    authToken = loginResponse.body.token;
  });
  
  test('should create new reservation', async () => {
    const reservationData = {
      customerName: 'Jane Smith',
      customerEmail: '<EMAIL>',
      partySize: 4,
      reservationDate: '2024-12-25',
      reservationTime: '19:00'
    };
    
    const response = await request(app)
      .post('/api/reservations')
      .set('Authorization', \`Bearer \${authToken}\`)
      .send(reservationData)
      .expect(201);
    
    expect(response.body.customerName).toBe(reservationData.customerName);
    expect(response.body.status).toBe('confirmed');
  });
});`
      },
      {
        name: 'Real-time Socket Communication',
        description: 'Test WebSocket connections and real-time reservation updates',
        type: 'integration',
        priority: 'medium',
        testCode: `describe('Socket.io Real-time Updates', () => {
  let clientSocket;
  let serverSocket;
  
  beforeAll((done) => {
    const server = require('../server/index');
    clientSocket = require('socket.io-client')('http://localhost:5000');
    
    server.on('connection', (socket) => {
      serverSocket = socket;
    });
    
    clientSocket.on('connect', done);
  });
  
  test('should receive reservation updates', (done) => {
    clientSocket.on('reservation-changed', (data) => {
      expect(data).toHaveProperty('reservationId');
      expect(data).toHaveProperty('status');
      done();
    });
    
    serverSocket.emit('reservation-update', {
      reservationId: '123',
      status: 'seated',
      restaurantId: 'rest-1'
    });
  });
});`
      },
      {
        name: 'Menu Management API',
        description: 'Test menu item creation, updates, and availability toggles',
        type: 'unit',
        priority: 'medium',
        testCode: `describe('Menu Management', () => {
  test('should add new menu item', async () => {
    const menuItem = {
      name: 'Grilled Salmon',
      description: 'Fresh Atlantic salmon with herbs',
      price: 24.99,
      category: 'main',
      allergens: ['fish'],
      available: true
    };
    
    const response = await request(app)
      .post('/api/menu/items')
      .set('Authorization', \`Bearer \${authToken}\`)
      .send(menuItem)
      .expect(201);
    
    expect(response.body.name).toBe(menuItem.name);
    expect(response.body.price).toBe(menuItem.price);
  });
  
  test('should toggle item availability', async () => {
    const itemId = 'item-123';
    
    const response = await request(app)
      .patch(\`/api/menu/items/\${itemId}/availability\`)
      .set('Authorization', \`Bearer \${authToken}\`)
      .send({ available: false })
      .expect(200);
    
    expect(response.body.available).toBe(false);
  });
});`
      },
      {
        name: 'Data Validation and Security',
        description: 'Test input validation, SQL injection prevention, and rate limiting',
        type: 'unit',
        priority: 'high',
        testCode: `describe('Security and Validation', () => {
  test('should reject invalid email format', async () => {
    const invalidUser = {
      name: 'Test User',
      email: 'invalid-email',
      password: 'password123'
    };
    
    await request(app)
      .post('/api/auth/register')
      .send(invalidUser)
      .expect(400);
  });
  
  test('should enforce rate limiting', async () => {
    const requests = Array(11).fill().map(() => 
      request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
    );
    
    const responses = await Promise.all(requests);
    const rateLimitedResponse = responses[responses.length - 1];
    
    expect(rateLimitedResponse.status).toBe(429);
  });
  
  test('should sanitize user input', async () => {
    const maliciousInput = {
      customerName: '<script>alert("xss")</script>',
      specialRequests: 'DROP TABLE reservations;'
    };
    
    const response = await request(app)
      .post('/api/reservations')
      .set('Authorization', \`Bearer \${authToken}\`)
      .send(maliciousInput)
      .expect(400);
    
    expect(response.body.error).toContain('Invalid input');
  });
});`
      }
    ];

    setTestCases(mockTests);
    setIsProcessing(false);
    setCurrentStep('complete');
  };

  const generateReport = () => {
    if (!selectedStack) return '';

    return `# Technical Architecture Report

## Selected Tech Stack: ${selectedStack.name}

### Overview
${selectedStack.description}

### Technologies
${selectedStack.technologies.map(tech => `- ${tech}`).join('\n')}

### Pros & Cons Analysis

**Pros:**
${selectedStack.pros.map(pro => `- ${pro}`).join('\n')}

**Cons:**
${selectedStack.cons.map(con => `- ${con}`).join('\n')}

**Metrics:**
- Complexity: ${selectedStack.complexity}
- Time to MVP: ${selectedStack.timeToMVP}
- Scalability: ${selectedStack.scalability}/10
- Cost: ${selectedStack.cost}

## System Architecture

\`\`\`text
${architecture.map(component => {
  const connections = component.connections.length > 0 ? ` → ${component.connections.join(', ')}` : '';
  return `${component.name} (${component.type})${connections}`;
}).join('\n')}
\`\`\`

### Component Details
${architecture.map(component => `
**${component.name}** (${component.type})
${component.description}
`).join('')}

## MVP Scaffold Structure

\`\`\`
${scaffoldFiles.map(file => file.path).join('\n')}
\`\`\`

### Key Files Generated
${scaffoldFiles.map(file => `
**${file.path}** (${file.type})
\`\`\`${file.type === 'code' ? 'javascript' : file.type === 'config' ? 'json' : 'markdown'}
${file.content.substring(0, 200)}...
\`\`\`
`).join('')}

## Automated Test Cases

${testCases.map((test, index) => `
### ${index + 1}. ${test.name} (${test.type} - ${test.priority} priority)
${test.description}

\`\`\`javascript
${test.testCode}
\`\`\`
`).join('')}

## LiveKit Demo Session
- **Demo URL:** ${demoSession.url}
- **Duration:** ${Math.floor(demoSession.duration / 60)}:${(demoSession.duration % 60).toString().padStart(2, '0')}

### Key Timestamps
${demoSession.timestamps.map(timestamp => `- **${timestamp.time}:** ${timestamp.description}`).join('\n')}

## Next Steps
1. Set up development environment
2. Initialize version control (Git)
3. Configure CI/CD pipeline
4. Set up monitoring and logging
5. Deploy to staging environment

---
*Generated by Technical Co-founder Agent | Architecture designed for scalability and maintainability*`;
  };

  const downloadReport = () => {
    const report = generateReport();
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'technical-architecture-report.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const downloadScaffold = () => {
    // Create a simple text representation of the scaffold
    const scaffoldContent = scaffoldFiles.map(file => 
      `=== ${file.path} ===\n${file.content}\n\n`
    ).join('');
    
    const blob = new Blob([scaffoldContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'mvp-scaffold.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <Code2 className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Technical Co-founder Agent
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Architect, scaffold, and demo your technical solution
        </p>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-center space-x-2 mb-8 overflow-x-auto">
        {[
          { id: 'intro', label: 'Introduction', icon: Code2 },
          { id: 'stacks', label: 'Tech Stacks', icon: Database },
          { id: 'architecture', label: 'Architecture', icon: Server },
          { id: 'scaffold', label: 'Scaffold', icon: Package },
          { id: 'demo', label: 'Demo', icon: Monitor },
          { id: 'tests', label: 'Tests', icon: CheckCircle },
          { id: 'complete', label: 'Report', icon: FileText }
        ].map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs ${
              currentStep === step.id 
                ? 'bg-green-600 text-white' 
                : index < ['intro', 'stacks', 'architecture', 'scaffold', 'demo', 'tests', 'complete'].indexOf(currentStep)
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
            }`}>
              <step.icon size={12} />
            </div>
            <span className="ml-1 text-xs font-medium text-gray-700 dark:text-gray-300 hidden sm:inline">
              {step.label}
            </span>
            {index < 6 && (
              <div className={`w-4 h-0.5 mx-2 ${
                index < ['intro', 'stacks', 'architecture', 'scaffold', 'demo', 'tests', 'complete'].indexOf(currentStep)
                  ? 'bg-green-500'
                  : 'bg-gray-200 dark:bg-gray-700'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        {currentStep === 'intro' && (
          <motion.div
            key="intro"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Let's Architect Your Technical Solution
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              I'll analyze three tech stack options, design system architecture, generate MVP scaffold code, conduct a 5-minute LiveKit screen-share demo, and create automated test cases for your core functionality.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <Database className="w-8 h-8 text-blue-500 mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white mb-1">Tech Stack Analysis</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Compare MERN, Django+Vue, and Serverless options</p>
              </div>
              <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <Server className="w-8 h-8 text-green-500 mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white mb-1">System Architecture</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Design scalable, maintainable architecture</p>
              </div>
              <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <Code2 className="w-8 h-8 text-purple-500 mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white mb-1">MVP Scaffold</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Generate production-ready code structure</p>
              </div>
            </div>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={generateTechStacks}
              className="w-full flex items-center justify-center gap-3 px-6 py-4 bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white rounded-lg font-semibold text-lg transition-all"
            >
              <Zap size={20} />
              Start Technical Analysis
            </motion.button>
          </motion.div>
        )}

        {currentStep === 'stacks' && (
          <motion.div
            key="stacks"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Analyzing Tech Stack Options...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Evaluating pros, cons, and technical requirements
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Tech Stack Options
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Choose the best technology stack for your project
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {techStacks.map((stack, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.2 }}
                      className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg cursor-pointer transition-all ${
                        selectedStack?.name === stack.name
                          ? 'ring-2 ring-green-500 shadow-green-500/20'
                          : 'hover:shadow-xl'
                      }`}
                      onClick={() => selectTechStack(stack)}
                    >
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                          {stack.name}
                        </h3>
                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                          stack.complexity === 'Low' ? 'bg-green-100 text-green-700' :
                          stack.complexity === 'Medium' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-red-100 text-red-700'
                        }`}>
                          {stack.complexity}
                        </div>
                      </div>

                      <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                        {stack.description}
                      </p>

                      <div className="space-y-3 mb-4">
                        <div>
                          <h4 className="font-medium text-green-700 dark:text-green-400 text-sm mb-1">Pros</h4>
                          <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                            {stack.pros.slice(0, 2).map((pro, i) => (
                              <li key={i} className="flex items-start gap-1">
                                <CheckCircle size={10} className="text-green-500 mt-0.5 flex-shrink-0" />
                                {pro}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-red-700 dark:text-red-400 text-sm mb-1">Cons</h4>
                          <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                            {stack.cons.slice(0, 2).map((con, i) => (
                              <li key={i} className="flex items-start gap-1">
                                <AlertTriangle size={10} className="text-red-500 mt-0.5 flex-shrink-0" />
                                {con}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-center">
                        <div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">Time to MVP</p>
                          <p className="font-medium text-gray-900 dark:text-white">{stack.timeToMVP}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">Scalability</p>
                          <p className="font-medium text-gray-900 dark:text-white">{stack.scalability}/10</p>
                        </div>
                      </div>

                      {selectedStack?.name === stack.name && (
                        <div className="mt-4 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg text-center">
                          <CheckCircle className="w-5 h-5 text-green-500 mx-auto mb-1" />
                          <p className="text-sm font-medium text-green-700 dark:text-green-400">Selected</p>
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'architecture' && (
          <motion.div
            key="architecture"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Designing System Architecture...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Creating scalable and maintainable architecture
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    System Architecture
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    High-level architecture for {selectedStack?.name}
                  </p>
                </div>

                {/* Architecture Diagram */}
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
                    Component Flow Diagram
                  </h3>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 font-mono text-sm">
                    <div className="space-y-2">
                      {architecture.map((component, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${
                            component.type === 'client' ? 'bg-blue-500' :
                            component.type === 'api' ? 'bg-green-500' :
                            component.type === 'service' ? 'bg-purple-500' :
                            component.type === 'database' ? 'bg-orange-500' :
                            'bg-gray-500'
                          }`} />
                          <span className="text-gray-900 dark:text-white">
                            {component.name}
                          </span>
                          {component.connections.length > 0 && (
                            <>
                              <span className="text-gray-500">→</span>
                              <span className="text-gray-600 dark:text-gray-400">
                                {component.connections.join(', ')}
                              </span>
                            </>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Component Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {architecture.map((component, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow"
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <div className={`w-3 h-3 rounded-full ${
                          component.type === 'client' ? 'bg-blue-500' :
                          component.type === 'api' ? 'bg-green-500' :
                          component.type === 'service' ? 'bg-purple-500' :
                          component.type === 'database' ? 'bg-orange-500' :
                          'bg-gray-500'
                        }`} />
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {component.name}
                        </h4>
                        <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-gray-600 dark:text-gray-400 capitalize">
                          {component.type}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {component.description}
                      </p>
                    </motion.div>
                  ))}
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'scaffold' && (
          <motion.div
            key="scaffold"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Generating MVP Scaffold...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Creating production-ready code structure
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    MVP Scaffold Generated
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Production-ready code structure for {selectedStack?.name}
                  </p>
                  
                  <div className="flex items-center justify-center gap-4 mt-4">
                    <button
                      onClick={downloadScaffold}
                      className="flex items-center gap-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium transition-colors"
                    >
                      <Download size={16} />
                      Download Scaffold
                    </button>
                  </div>
                </div>

                {/* File Structure */}
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
                    Project Structure
                  </h3>
                  <div className="bg-gray-900 rounded-lg p-4 font-mono text-sm text-green-400">
                    {scaffoldFiles.map((file, index) => (
                      <div key={index} className="flex items-center gap-2 py-1">
                        <span className="text-gray-500">
                          {file.type === 'config' ? '⚙️' : 
                           file.type === 'code' ? '📄' : 
                           file.type === 'docs' ? '📚' : '🧪'}
                        </span>
                        <span>{file.path}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Key Files Preview */}
                <div className="space-y-4">
                  {scaffoldFiles.slice(0, 3).map((file, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.2 }}
                      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {file.path}
                        </h4>
                        <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-gray-600 dark:text-gray-400 capitalize">
                          {file.type}
                        </span>
                      </div>
                      
                      <div className="rounded-lg overflow-hidden">
                        <SyntaxHighlighter
                          language={
                            file.type === 'code' ? 'javascript' : 
                            file.type === 'config' ? 'json' : 
                            'markdown'
                          }
                          style={vscDarkPlus}
                          customStyle={{
                            margin: 0,
                            fontSize: '0.75rem',
                            maxHeight: '200px'
                          }}
                        >
                          {file.content.substring(0, 500) + (file.content.length > 500 ? '...' : '')}
                        </SyntaxHighlighter>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'demo' && (
          <motion.div
            key="demo"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <Monitor className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                LiveKit Screen-Share Demo
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Duration: {Math.floor(demoSession.duration / 60)}:{(demoSession.duration % 60).toString().padStart(2, '0')} / 5:00
              </p>
            </div>

            {/* Demo Interface */}
            <div className="bg-gray-900 rounded-lg p-6 mb-6 aspect-video relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <Share2 className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg mb-2">Screen Share Active</p>
                  <p className="text-sm opacity-75">Walking through technical architecture and code</p>
                </div>
              </div>
              
              {/* Demo Controls */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2">
                <button className="p-2 bg-gray-800/80 text-white rounded-full hover:bg-gray-700/80">
                  <Share2 size={16} />
                </button>
                <button className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600">
                  <Pause size={16} />
                </button>
              </div>
            </div>

            {/* Demo Timeline */}
            <div className="mb-6">
              <h3 className="font-medium text-gray-900 dark:text-white mb-3">Demo Timeline</h3>
              <div className="space-y-2">
                {demoSession.timestamps.map((timestamp, index) => (
                  <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <span className="font-mono text-sm text-blue-600 dark:text-blue-400 min-w-12">
                      {timestamp.time}
                    </span>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {timestamp.description}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={endDemo}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium transition-colors"
              >
                <SkipForward size={16} />
                Complete Demo
              </button>
              <button className="px-4 py-3 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                <Pause size={16} />
              </button>
            </div>
          </motion.div>
        )}

        {currentStep === 'tests' && (
          <motion.div
            key="tests"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Generating Test Cases...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Creating automated tests for core functionality
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Automated Test Cases
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Comprehensive test suite for quality assurance
                  </p>
                </div>

                <div className="space-y-4">
                  {testCases.map((test, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <CheckCircle className="w-6 h-6 text-green-500" />
                          <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                            {test.name}
                          </h3>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                            test.type === 'unit' ? 'bg-blue-100 text-blue-700' :
                            test.type === 'integration' ? 'bg-purple-100 text-purple-700' :
                            'bg-green-100 text-green-700'
                          }`}>
                            {test.type}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                            test.priority === 'high' ? 'bg-red-100 text-red-700' :
                            test.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                            'bg-gray-100 text-gray-700'
                          }`}>
                            {test.priority}
                          </span>
                        </div>
                      </div>

                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {test.description}
                      </p>

                      <div className="rounded-lg overflow-hidden">
                        <SyntaxHighlighter
                          language="javascript"
                          style={vscDarkPlus}
                          customStyle={{
                            margin: 0,
                            fontSize: '0.75rem',
                            maxHeight: '200px'
                          }}
                        >
                          {test.testCode}
                        </SyntaxHighlighter>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'complete' && (
          <motion.div
            key="complete"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Technical Architecture Complete
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Your comprehensive technical solution is ready
              </p>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Database className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">3</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Tech Stacks</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Server className="w-8 h-8 text-green-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{architecture.length}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Components</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Package className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{scaffoldFiles.length}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Files</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <CheckCircle className="w-8 h-8 text-orange-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{testCases.length}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Test Cases</p>
              </div>
            </div>

            {/* Selected Stack Summary */}
            {selectedStack && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
                  Selected: {selectedStack.name}
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <p className="text-sm text-gray-600 dark:text-gray-400">Complexity</p>
                    <p className="font-bold text-gray-900 dark:text-white">{selectedStack.complexity}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600 dark:text-gray-400">Time to MVP</p>
                    <p className="font-bold text-gray-900 dark:text-white">{selectedStack.timeToMVP}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600 dark:text-gray-400">Scalability</p>
                    <p className="font-bold text-gray-900 dark:text-white">{selectedStack.scalability}/10</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600 dark:text-gray-400">Cost</p>
                    <p className="font-bold text-gray-900 dark:text-white">{selectedStack.cost}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={downloadReport}
                className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white rounded-lg font-medium transition-all"
              >
                <Download size={16} />
                Download Technical Report
              </motion.button>
              <button
                onClick={downloadScaffold}
                className="flex items-center gap-2 px-6 py-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
              >
                <Package size={16} />
                Download Scaffold
              </button>
              <button
                onClick={() => {
                  setCurrentStep('intro');
                  setSelectedStack(null);
                  setTechStacks([]);
                  setArchitecture([]);
                  setScaffoldFiles([]);
                  setTestCases([]);
                  setDemoSession(prev => ({ ...prev, duration: 0, timestamps: [] }));
                }}
                className="px-6 py-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
              >
                New Analysis
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};