import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUp, 
  Target, 
  Calendar, 
  Lightbulb,
  Mic,
  MicOff,
  Video,
  VideoOff,
  Phone,
  PhoneOff,
  Download,
  FileText,
  BarChart3,
  Megaphone,
  Users,
  Share2,
  Play,
  Pause,
  SkipForward,
  Star,
  DollarSign,
  Globe,
  Zap
} from 'lucide-react';

interface BrandPositioning {
  tagline: string;
  valueProposition: string;
  targetAudience: string;
  keyDifferentiators: string[];
  brandPersonality: string[];
}

interface ContentCalendarItem {
  week: number;
  topic: string;
  channels: string[];
  contentType: string;
  objective: string;
}

interface GrowthHack {
  name: string;
  description: string;
  cost: number;
  effort: 'low' | 'medium' | 'high';
  expectedROI: string;
  timeline: string;
  category: 'viral' | 'partnership' | 'content' | 'product';
}

interface PressRelease {
  headline: string;
  subheadline: string;
  lede: string;
  keyPoints: string[];
  quotes: Array<{
    person: string;
    title: string;
    quote: string;
  }>;
}

interface PitchSession {
  url: string;
  isActive: boolean;
  duration: number;
  feedback: Array<{
    timestamp: number;
    type: 'positive' | 'improvement' | 'suggestion';
    comment: string;
  }>;
  overallScore: number;
}

export const MarketingAgent: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<'intro' | 'positioning' | 'content' | 'growth' | 'press' | 'pitch' | 'complete'>('intro');
  const [brandPositioning, setBrandPositioning] = useState<BrandPositioning | null>(null);
  const [contentCalendar, setContentCalendar] = useState<ContentCalendarItem[]>([]);
  const [growthHacks, setGrowthHacks] = useState<GrowthHack[]>([]);
  const [pressRelease, setPressRelease] = useState<PressRelease | null>(null);
  const [pitchSession, setPitchSession] = useState<PitchSession>({
    url: '',
    isActive: false,
    duration: 0,
    feedback: [],
    overallScore: 0
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [businessIdea, setBusinessIdea] = useState('');

  const generateBrandPositioning = async () => {
    setIsProcessing(true);
    setCurrentStep('positioning');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const mockPositioning: BrandPositioning = {
      tagline: "Your AI Co-founder Team, Ready in Minutes",
      valueProposition: "Transform your startup journey with 5 specialized AI co-founders that provide strategic, product, technical, operations, and marketing expertise through real-time collaboration.",
      targetAudience: "Early-stage entrepreneurs, solo founders, and small startup teams seeking comprehensive business guidance and accelerated growth.",
      keyDifferentiators: [
        "Multi-agent AI system with specialized business roles",
        "Real-time video/voice collaboration via LiveKit",
        "Integrated code generation and business strategy",
        "Comprehensive tool integrations via Composio",
        "End-to-end startup support from idea to scale"
      ],
      brandPersonality: [
        "Innovative & Forward-thinking",
        "Reliable & Trustworthy", 
        "Collaborative & Supportive",
        "Results-driven & Efficient",
        "Accessible & User-friendly"
      ]
    };

    setBrandPositioning(mockPositioning);
    setIsProcessing(false);
    generateContentCalendar();
  };

  const generateContentCalendar = async () => {
    setIsProcessing(true);
    setCurrentStep('content');
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const mockContentCalendar: ContentCalendarItem[] = [
      {
        week: 1,
        topic: "Launch Announcement",
        channels: ["Twitter", "LinkedIn", "Product Hunt"],
        contentType: "Video + Blog Post",
        objective: "Generate awareness and initial signups"
      },
      {
        week: 2,
        topic: "AI Co-founder Demo Series",
        channels: ["YouTube", "Twitter", "LinkedIn"],
        contentType: "Demo Videos",
        objective: "Showcase product capabilities"
      },
      {
        week: 3,
        topic: "Founder Success Stories",
        channels: ["Blog", "LinkedIn", "Newsletter"],
        contentType: "Case Studies",
        objective: "Build credibility and social proof"
      },
      {
        week: 4,
        topic: "Behind the Scenes: AI Development",
        channels: ["Twitter", "YouTube", "Blog"],
        contentType: "Technical Content",
        objective: "Establish thought leadership"
      },
      {
        week: 5,
        topic: "Startup Tips & Best Practices",
        channels: ["LinkedIn", "Twitter", "Newsletter"],
        contentType: "Educational Content",
        objective: "Provide value and build community"
      },
      {
        week: 6,
        topic: "Integration Spotlight",
        channels: ["Blog", "YouTube", "Twitter"],
        contentType: "Tutorial Videos",
        objective: "Highlight platform capabilities"
      },
      {
        week: 7,
        topic: "Community Challenges",
        channels: ["Twitter", "LinkedIn", "Discord"],
        contentType: "Interactive Content",
        objective: "Engage users and build community"
      },
      {
        week: 8,
        topic: "Product Updates & Roadmap",
        channels: ["Blog", "Newsletter", "Twitter"],
        contentType: "Product Announcements",
        objective: "Keep users informed and excited"
      },
      {
        week: 9,
        topic: "Expert Interviews",
        channels: ["YouTube", "LinkedIn", "Podcast"],
        contentType: "Interview Series",
        objective: "Build authority and network"
      },
      {
        week: 10,
        topic: "User-Generated Content",
        channels: ["Twitter", "LinkedIn", "Instagram"],
        contentType: "Community Highlights",
        objective: "Showcase user success and engagement"
      },
      {
        week: 11,
        topic: "Industry Insights & Trends",
        channels: ["Blog", "LinkedIn", "Newsletter"],
        contentType: "Thought Leadership",
        objective: "Position as industry expert"
      },
      {
        week: 12,
        topic: "Milestone Celebration",
        channels: ["All Channels"],
        contentType: "Celebration Content",
        objective: "Build momentum and community pride"
      }
    ];

    setContentCalendar(mockContentCalendar);
    setIsProcessing(false);
    generateGrowthHacks();
  };

  const generateGrowthHacks = async () => {
    setIsProcessing(true);
    setCurrentStep('growth');
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const mockGrowthHacks: GrowthHack[] = [
      {
        name: "AI Startup Accelerator Partnerships",
        description: "Partner with Y Combinator, Techstars, and other accelerators to offer exclusive access to portfolio companies",
        cost: 2500,
        effort: 'medium',
        expectedROI: "500+ qualified leads, 15% conversion",
        timeline: "Month 1-2",
        category: 'partnership'
      },
      {
        name: "Viral LinkedIn Challenge",
        description: "Launch #AICofounderChallenge where founders share their biggest startup struggles and get AI co-founder solutions",
        cost: 500,
        effort: 'low',
        expectedROI: "50K+ impressions, 1000+ signups",
        timeline: "Week 2-3",
        category: 'viral'
      },
      {
        name: "Freemium Product Hunt Launch",
        description: "Strategic Product Hunt launch with free tier, influencer outreach, and coordinated social media campaign",
        cost: 1500,
        effort: 'high',
        expectedROI: "Top 5 daily ranking, 2000+ signups",
        timeline: "Month 1",
        category: 'product'
      },
      {
        name: "Startup Podcast Tour",
        description: "Appear on 20+ startup podcasts sharing AI co-founder insights and platform benefits",
        cost: 800,
        effort: 'medium',
        expectedROI: "10K+ qualified traffic, 8% conversion",
        timeline: "Month 2-3",
        category: 'content'
      }
    ];

    setGrowthHacks(mockGrowthHacks);
    setIsProcessing(false);
    generatePressRelease();
  };

  const generatePressRelease = async () => {
    setIsProcessing(true);
    setCurrentStep('press');
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockPressRelease: PressRelease = {
      headline: "Revolutionary AI Platform Launches 5-Agent Co-founder System for Startups",
      subheadline: "Ultimate Startup Co-founder combines strategic, product, technical, operations, and marketing AI agents with real-time collaboration",
      lede: "Today, Ultimate Startup Co-founder launches the world's first comprehensive AI co-founder platform, featuring five specialized AI agents that provide strategic planning, product development, technical architecture, operational setup, and marketing strategy through seamless video and voice collaboration powered by LiveKit technology.",
      keyPoints: [
        "First-of-its-kind 5-agent AI co-founder system covering all business functions",
        "Real-time video/voice collaboration with AI agents via LiveKit integration",
        "Comprehensive tool integrations through Composio platform",
        "End-to-end startup support from ideation to market launch",
        "Designed for solo founders and early-stage startup teams"
      ],
      quotes: [
        {
          person: "Alex Strategic",
          title: "Strategic Co-founder Agent",
          quote: "We're not just providing AI assistance – we're delivering a complete co-founder experience that guides entrepreneurs through every critical business decision with expert-level insights."
        },
        {
          person: "Industry Expert",
          title: "Startup Accelerator Partner",
          quote: "This platform addresses the biggest challenge facing solo founders: having access to diverse expertise across all business functions. It's like having a complete founding team from day one."
        }
      ]
    };

    setPressRelease(mockPressRelease);
    setIsProcessing(false);
  };

  const startPitchRehearsal = () => {
    setPitchSession({
      url: 'wss://marketing-cofounder.livekit.cloud',
      isActive: true,
      duration: 0,
      feedback: [],
      overallScore: 0
    });
    setCurrentStep('pitch');

    // Simulate 5-minute pitch session
    const timer = setInterval(() => {
      setPitchSession(prev => ({
        ...prev,
        duration: prev.duration + 1
      }));
    }, 1000);

    // Add feedback at intervals
    setTimeout(() => {
      setPitchSession(prev => ({
        ...prev,
        feedback: [...prev.feedback, {
          timestamp: 30,
          type: 'positive',
          comment: 'Great opening hook! You immediately captured attention with the problem statement.'
        }]
      }));
    }, 30000);

    setTimeout(() => {
      setPitchSession(prev => ({
        ...prev,
        feedback: [...prev.feedback, {
          timestamp: 90,
          type: 'improvement',
          comment: 'Consider adding specific metrics or examples to strengthen your value proposition.'
        }]
      }));
    }, 90000);

    setTimeout(() => {
      setPitchSession(prev => ({
        ...prev,
        feedback: [...prev.feedback, {
          timestamp: 150,
          type: 'suggestion',
          comment: 'Your demo was compelling. Try to emphasize the ROI and time savings more clearly.'
        }]
      }));
    }, 150000);

    setTimeout(() => {
      clearInterval(timer);
      endPitchSession();
    }, 300000); // 5 minutes
  };

  const endPitchSession = () => {
    setPitchSession(prev => ({
      ...prev,
      isActive: false,
      overallScore: 8.5,
      feedback: [...prev.feedback, {
        timestamp: prev.duration,
        type: 'positive',
        comment: 'Strong closing! You clearly articulated next steps and the call to action.'
      }]
    }));
    setCurrentStep('complete');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getEffortColor = (effort: string) => {
    switch (effort) {
      case 'low':
        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-300';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'high':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'viral':
        return <Share2 className="w-4 h-4" />;
      case 'partnership':
        return <Users className="w-4 h-4" />;
      case 'content':
        return <FileText className="w-4 h-4" />;
      case 'product':
        return <Star className="w-4 h-4" />;
      default:
        return <Zap className="w-4 h-4" />;
    }
  };

  const generateReport = () => {
    if (!brandPositioning || !pressRelease) return '';

    return `# Marketing Strategy Report

## Brand Positioning

**Tagline:** "${brandPositioning.tagline}"

**Value Proposition:** ${brandPositioning.valueProposition}

**Target Audience:** ${brandPositioning.targetAudience}

### Key Differentiators
${brandPositioning.keyDifferentiators.map((diff, i) => `${i + 1}. ${diff}`).join('\n')}

### Brand Personality
${brandPositioning.brandPersonality.map(trait => `- ${trait}`).join('\n')}

## 3-Month Content Calendar

| Week | Topic | Channels | Content Type | Objective |
|------|-------|----------|--------------|-----------|
${contentCalendar.map(item => 
  `| ${item.week} | ${item.topic} | ${item.channels.join(', ')} | ${item.contentType} | ${item.objective} |`
).join('\n')}

## Growth Hacks

${growthHacks.map((hack, index) => `
### ${index + 1}. ${hack.name} (${hack.category.toUpperCase()})
- **Cost:** ${formatCurrency(hack.cost)}
- **Effort:** ${hack.effort}
- **Expected ROI:** ${hack.expectedROI}
- **Timeline:** ${hack.timeline}
- **Description:** ${hack.description}
`).join('')}

### Total Growth Investment: ${formatCurrency(growthHacks.reduce((total, hack) => total + hack.cost, 0))}

## Press Release

**Headline:** ${pressRelease.headline}

**Subheadline:** ${pressRelease.subheadline}

**Opening Paragraph:** ${pressRelease.lede}

### Key Points
${pressRelease.keyPoints.map(point => `- ${point}`).join('\n')}

### Quotes
${pressRelease.quotes.map(quote => `
**${quote.person}, ${quote.title}:**
"${quote.quote}"
`).join('')}

## Pitch Rehearsal Results

**Session Duration:** ${Math.floor(pitchSession.duration / 60)}:${(pitchSession.duration % 60).toString().padStart(2, '0')}
**Overall Score:** ${pitchSession.overallScore}/10
**Recording URL:** ${pitchSession.url}

### Feedback Summary
${pitchSession.feedback.map(fb => `
- **${fb.timestamp}s (${fb.type.toUpperCase()}):** ${fb.comment}
`).join('')}

---
*Generated by Marketing Co-founder Agent | Pitch Session Score: ${pitchSession.overallScore}/10*`;
  };

  const downloadReport = () => {
    const report = generateReport();
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'marketing-strategy-report.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-pink-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <Megaphone className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Marketing Co-founder Agent
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Position, acquire & galvanize users
        </p>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-center space-x-2 mb-8 overflow-x-auto">
        {[
          { id: 'intro', label: 'Introduction', icon: Megaphone },
          { id: 'positioning', label: 'Positioning', icon: Target },
          { id: 'content', label: 'Content', icon: Calendar },
          { id: 'growth', label: 'Growth', icon: TrendingUp },
          { id: 'press', label: 'Press', icon: FileText },
          { id: 'pitch', label: 'Pitch', icon: Video },
          { id: 'complete', label: 'Report', icon: BarChart3 }
        ].map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs ${
              currentStep === step.id 
                ? 'bg-pink-600 text-white' 
                : index < ['intro', 'positioning', 'content', 'growth', 'press', 'pitch', 'complete'].indexOf(currentStep)
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
            }`}>
              <step.icon size={12} />
            </div>
            <span className="ml-1 text-xs font-medium text-gray-700 dark:text-gray-300 hidden sm:inline">
              {step.label}
            </span>
            {index < 6 && (
              <div className={`w-4 h-0.5 mx-2 ${
                index < ['intro', 'positioning', 'content', 'growth', 'press', 'pitch', 'complete'].indexOf(currentStep)
                  ? 'bg-green-500'
                  : 'bg-gray-200 dark:bg-gray-700'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        {currentStep === 'intro' && (
          <motion.div
            key="intro"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Let's Build Your Marketing Strategy
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              I'll craft your brand positioning, create a 3-month content calendar, ideate growth hacks, draft a press release, and conduct a 5-minute LiveKit pitch rehearsal with real-time feedback.
            </p>
            
            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Business Idea (Optional)
                </label>
                <textarea
                  value={businessIdea}
                  onChange={(e) => setBusinessIdea(e.target.value)}
                  placeholder="Briefly describe your business to help me create targeted marketing strategies..."
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  rows={3}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="p-4 bg-pink-50 dark:bg-pink-900/20 rounded-lg text-center">
                <Target className="w-8 h-8 text-pink-500 mx-auto mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white">Brand Positioning</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Tagline & value prop</p>
              </div>
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-center">
                <Calendar className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white">Content Calendar</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">12-week plan</p>
              </div>
              <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg text-center">
                <Lightbulb className="w-8 h-8 text-green-500 mx-auto mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white">Growth Hacks</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">4 strategies</p>
              </div>
              <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg text-center">
                <Video className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white">Pitch Rehearsal</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">5-min session</p>
              </div>
            </div>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={generateBrandPositioning}
              className="w-full flex items-center justify-center gap-3 px-6 py-4 bg-gradient-to-r from-pink-500 to-orange-600 hover:from-pink-600 hover:to-orange-700 text-white rounded-lg font-semibold text-lg transition-all"
            >
              <Megaphone size={20} />
              Start Marketing Strategy
            </motion.button>
          </motion.div>
        )}

        {currentStep === 'positioning' && (
          <motion.div
            key="positioning"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Crafting Brand Positioning...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Developing tagline, value proposition, and brand personality
                </p>
              </div>
            ) : brandPositioning && (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Brand Positioning
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Your brand foundation and messaging strategy
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                  <div className="text-center mb-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-pink-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Target className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                      "{brandPositioning.tagline}"
                    </h3>
                    <p className="text-lg text-gray-600 dark:text-gray-400">
                      Your Brand Tagline
                    </p>
                  </div>

                  <div className="space-y-6">
                    <div className="bg-pink-50 dark:bg-pink-900/20 rounded-lg p-6">
                      <h4 className="font-bold text-pink-900 dark:text-pink-200 mb-3">Value Proposition</h4>
                      <p className="text-pink-700 dark:text-pink-300">
                        {brandPositioning.valueProposition}
                      </p>
                    </div>

                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
                      <h4 className="font-bold text-blue-900 dark:text-blue-200 mb-3">Target Audience</h4>
                      <p className="text-blue-700 dark:text-blue-300">
                        {brandPositioning.targetAudience}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
                        <h4 className="font-bold text-green-900 dark:text-green-200 mb-3">Key Differentiators</h4>
                        <ul className="space-y-2">
                          {brandPositioning.keyDifferentiators.map((diff, index) => (
                            <li key={index} className="flex items-start gap-2 text-green-700 dark:text-green-300">
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                              {diff}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6">
                        <h4 className="font-bold text-purple-900 dark:text-purple-200 mb-3">Brand Personality</h4>
                        <div className="flex flex-wrap gap-2">
                          {brandPositioning.brandPersonality.map((trait, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-purple-200 dark:bg-purple-800 text-purple-800 dark:text-purple-200 text-sm rounded-full"
                            >
                              {trait}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'content' && (
          <motion.div
            key="content"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Building Content Calendar...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Creating 12-week content strategy with topics and channels
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    3-Month Content Calendar
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Strategic content plan across multiple channels
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200 dark:border-gray-700">
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Week</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Topic</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Channels</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Content Type</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Objective</th>
                      </tr>
                    </thead>
                    <tbody>
                      {contentCalendar.map((item, index) => (
                        <motion.tr
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className="border-b border-gray-100 dark:border-gray-700"
                        >
                          <td className="py-4 px-4 font-medium text-gray-900 dark:text-white">
                            {item.week}
                          </td>
                          <td className="py-4 px-4 text-gray-900 dark:text-white">
                            {item.topic}
                          </td>
                          <td className="py-4 px-4">
                            <div className="flex flex-wrap gap-1">
                              {item.channels.map((channel, i) => (
                                <span
                                  key={i}
                                  className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs rounded-full"
                                >
                                  {channel}
                                </span>
                              ))}
                            </div>
                          </td>
                          <td className="py-4 px-4 text-gray-900 dark:text-white">
                            {item.contentType}
                          </td>
                          <td className="py-4 px-4 text-gray-600 dark:text-gray-400 text-sm">
                            {item.objective}
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Content Insights */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
                    <Calendar className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">12</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Weeks Planned</p>
                  </div>
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
                    <Globe className="w-8 h-8 text-green-500 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-green-600 dark:text-green-400">6</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Channels</p>
                  </div>
                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 text-center">
                    <FileText className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">8</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Content Types</p>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'growth' && (
          <motion.div
            key="growth"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Ideating Growth Hacks...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Creating innovative growth strategies with cost analysis
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Growth Hacks
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    4 innovative strategies with cost and ROI analysis
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {growthHacks.map((hack, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white ${
                            hack.category === 'viral' ? 'bg-pink-500' :
                            hack.category === 'partnership' ? 'bg-blue-500' :
                            hack.category === 'content' ? 'bg-green-500' : 'bg-purple-500'
                          }`}>
                            {getCategoryIcon(hack.category)}
                          </div>
                          <div>
                            <h3 className="font-bold text-gray-900 dark:text-white">
                              {hack.name}
                            </h3>
                            <span className="text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full uppercase">
                              {hack.category}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                        {hack.description}
                      </p>
                      
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-500 dark:text-gray-400">Cost:</span>
                          <span className="font-bold text-gray-900 dark:text-white">
                            {formatCurrency(hack.cost)}
                          </span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-500 dark:text-gray-400">Effort:</span>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getEffortColor(hack.effort)}`}>
                            {hack.effort.toUpperCase()}
                          </span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-500 dark:text-gray-400">Timeline:</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {hack.timeline}
                          </span>
                        </div>
                        
                        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
                          <p className="text-sm font-medium text-green-800 dark:text-green-200">
                            Expected ROI: {hack.expectedROI}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Growth Summary */}
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Growth Investment Summary</h3>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <DollarSign className="w-8 h-8 text-green-500 mx-auto mb-2" />
                      <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(growthHacks.reduce((total, hack) => total + hack.cost, 0))}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Total Investment</p>
                    </div>
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <Lightbulb className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                      <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {growthHacks.length}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Growth Strategies</p>
                    </div>
                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <Target className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                      <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {growthHacks.filter(h => h.effort === 'low').length}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Low Effort</p>
                    </div>
                    <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                      <TrendingUp className="w-8 h-8 text-orange-500 mx-auto mb-2" />
                      <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                        {growthHacks.filter(h => h.category === 'viral').length}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Viral Potential</p>
                    </div>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'press' && (
          <motion.div
            key="press"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Drafting Press Release...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Creating compelling press release with headlines and quotes
                </p>
              </div>
            ) : pressRelease && (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Press Release
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Ready-to-publish announcement for media distribution
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                  <div className="space-y-6">
                    <div className="text-center border-b border-gray-200 dark:border-gray-700 pb-6">
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                        {pressRelease.headline}
                      </h3>
                      <p className="text-lg text-gray-600 dark:text-gray-400">
                        {pressRelease.subheadline}
                      </p>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                      <h4 className="font-bold text-gray-900 dark:text-white mb-3">Opening Paragraph</h4>
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        {pressRelease.lede}
                      </p>
                    </div>

                    <div>
                      <h4 className="font-bold text-gray-900 dark:text-white mb-3">Key Points</h4>
                      <ul className="space-y-2">
                        {pressRelease.keyPoints.map((point, index) => (
                          <li key={index} className="flex items-start gap-3">
                            <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mt-0.5">
                              {index + 1}
                            </div>
                            <p className="text-gray-700 dark:text-gray-300">{point}</p>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-bold text-gray-900 dark:text-white mb-4">Quotes</h4>
                      <div className="space-y-4">
                        {pressRelease.quotes.map((quote, index) => (
                          <div key={index} className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border-l-4 border-blue-500">
                            <p className="text-blue-800 dark:text-blue-200 italic mb-2">
                              "{quote.quote}"
                            </p>
                            <p className="text-blue-600 dark:text-blue-400 font-medium">
                              — {quote.person}, {quote.title}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={startPitchRehearsal}
                    className="flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white rounded-lg font-medium transition-all"
                  >
                    <Video size={16} />
                    Start Pitch Rehearsal
                  </motion.button>
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'pitch' && (
          <motion.div
            key="pitch"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <Video className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                LiveKit Pitch Rehearsal Active
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Duration: {Math.floor(pitchSession.duration / 60)}:{(pitchSession.duration % 60).toString().padStart(2, '0')} / 5:00
              </p>
            </div>

            {/* Video Interface */}
            <div className="bg-gray-900 rounded-lg p-6 mb-6 aspect-video relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <Video className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg mb-2">Pitch Rehearsal in Progress</p>
                  <p className="text-sm opacity-75">Practicing your marketing pitch with AI feedback</p>
                </div>
              </div>
              
              {/* Video Controls */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2">
                <button className="p-2 bg-gray-800/80 text-white rounded-full hover:bg-gray-700/80">
                  <Mic size={16} />
                </button>
                <button className="p-2 bg-gray-800/80 text-white rounded-full hover:bg-gray-700/80">
                  <Video size={16} />
                </button>
                <button className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600">
                  <PhoneOff size={16} />
                </button>
              </div>
            </div>

            {/* Real-time Feedback */}
            <div className="mb-6">
              <h3 className="font-medium text-gray-900 dark:text-white mb-3">Real-time Feedback</h3>
              <div className="space-y-3 max-h-40 overflow-y-auto">
                {pitchSession.feedback.map((feedback, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className={`p-3 rounded-lg ${
                      feedback.type === 'positive' ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500' :
                      feedback.type === 'improvement' ? 'bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-500' :
                      'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500'
                    }`}
                  >
                    <div className="flex items-start gap-2">
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                        {Math.floor(feedback.timestamp / 60)}:{(feedback.timestamp % 60).toString().padStart(2, '0')}
                      </span>
                      <p className={`text-sm ${
                        feedback.type === 'positive' ? 'text-green-700 dark:text-green-300' :
                        feedback.type === 'improvement' ? 'text-yellow-700 dark:text-yellow-300' :
                        'text-blue-700 dark:text-blue-300'
                      }`}>
                        {feedback.comment}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={endPitchSession}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium transition-colors"
              >
                <SkipForward size={16} />
                Complete Rehearsal
              </button>
              <button className="px-4 py-3 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                <Pause size={16} />
              </button>
            </div>
          </motion.div>
        )}

        {currentStep === 'complete' && (
          <motion.div
            key="complete"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Marketing Strategy Complete
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Your comprehensive marketing plan is ready for execution
              </p>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Target className="w-8 h-8 text-pink-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">1</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Brand Position</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Calendar className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{contentCalendar.length}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Content Weeks</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Lightbulb className="w-8 h-8 text-green-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{growthHacks.length}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Growth Hacks</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Video className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{pitchSession.overallScore}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Pitch Score</p>
              </div>
            </div>

            {/* Pitch Results */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Pitch Rehearsal Results</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-lg">{pitchSession.overallScore}</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">Overall Score</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Out of 10</p>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Session Duration: {Math.floor(pitchSession.duration / 60)}:{(pitchSession.duration % 60).toString().padStart(2, '0')}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Key Feedback</h4>
                  <div className="space-y-2">
                    {pitchSession.feedback.slice(0, 3).map((feedback, index) => (
                      <p key={index} className="text-sm text-gray-600 dark:text-gray-400">
                        • {feedback.comment}
                      </p>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={downloadReport}
                className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-pink-500 to-orange-600 hover:from-pink-600 hover:to-orange-700 text-white rounded-lg font-medium transition-all"
              >
                <Download size={16} />
                Download Marketing Report
              </motion.button>
              <button
                onClick={() => {
                  setCurrentStep('intro');
                  setBrandPositioning(null);
                  setContentCalendar([]);
                  setGrowthHacks([]);
                  setPressRelease(null);
                  setPitchSession(prev => ({ ...prev, feedback: [], duration: 0, overallScore: 0 }));
                }}
                className="px-6 py-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
              >
                New Strategy
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};