import React from 'react';
import { Link } from 'react-router-dom';
import { 
  FileText, 
  Code, 
  Video, 
  Book, 
  Zap, 
  ChevronRight, 
  Search,
  ArrowLeft,
  ExternalLink,
  Copy,
  Check
} from 'lucide-react';

export const Documentation: React.FC = () => {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [activeTab, setActiveTab] = React.useState('guides');
  const [copiedCode, setCopiedCode] = React.useState<string | null>(null);

  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(code);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">Documentation</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search docs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-64 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white"
                />
              </div>
              <Link to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Sidebar */}
          <div className="w-full md:w-64 flex-shrink-0">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 sticky top-24">
              <h2 className="font-bold text-gray-900 dark:text-white mb-4">Documentation</h2>
              
              <div className="space-y-1 mb-6">
                <button
                  onClick={() => setActiveTab('guides')}
                  className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                    activeTab === 'guides'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Book className="w-4 h-4" />
                    <span>Guides</span>
                  </div>
                  {activeTab === 'guides' && <ChevronRight className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => setActiveTab('api')}
                  className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                    activeTab === 'api'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Code className="w-4 h-4" />
                    <span>API Reference</span>
                  </div>
                  {activeTab === 'api' && <ChevronRight className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => setActiveTab('tutorials')}
                  className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                    activeTab === 'tutorials'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Video className="w-4 h-4" />
                    <span>Tutorials</span>
                  </div>
                  {activeTab === 'tutorials' && <ChevronRight className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => setActiveTab('examples')}
                  className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                    activeTab === 'examples'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    <span>Examples</span>
                  </div>
                  {activeTab === 'examples' && <ChevronRight className="w-4 h-4" />}
                </button>
              </div>

              <h3 className="font-medium text-gray-900 dark:text-white mb-2">Popular Topics</h3>
              <ul className="space-y-1 text-sm">
                <li>
                  <a href="#getting-started" className="text-purple-600 dark:text-purple-400 hover:underline">Getting Started</a>
                </li>
                <li>
                  <a href="#agent-communication" className="text-purple-600 dark:text-purple-400 hover:underline">Agent Communication</a>
                </li>
                <li>
                  <a href="#livekit-integration" className="text-purple-600 dark:text-purple-400 hover:underline">LiveKit Integration</a>
                </li>
                <li>
                  <a href="#bolt-integration" className="text-purple-600 dark:text-purple-400 hover:underline">Bolt.new Integration</a>
                </li>
              </ul>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              {activeTab === 'guides' && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Getting Started</h2>
                  
                  <div className="prose dark:prose-invert max-w-none">
                    <p>
                      Welcome to the Ultimate Co-founder documentation! This guide will help you get started with our AI-powered startup co-founder platform.
                    </p>

                    <h3 id="getting-started">Getting Started</h3>
                    <p>
                      Ultimate Co-founder provides a team of specialized AI agents to help you build and scale your startup. Each agent has unique expertise in strategic planning, product development, technical implementation, operations, and marketing.
                    </p>

                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 my-4">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">Quick Start</h4>
                      <ol className="list-decimal list-inside space-y-2 text-gray-700 dark:text-gray-300">
                        <li>Create an account or sign in</li>
                        <li>Describe your startup idea in the chat</li>
                        <li>Receive comprehensive analysis from all five co-founders</li>
                        <li>Dive deeper with specialized agents for specific domains</li>
                        <li>Use LiveKit for real-time video/voice collaboration</li>
                      </ol>
                    </div>

                    <h3 id="agent-communication">Agent Communication</h3>
                    <p>
                      You can communicate with all co-founders at once or select a specific agent for specialized guidance:
                    </p>

                    <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300">
                      <li><strong>Strategic Co-founder:</strong> Market analysis, competitive research, and business strategy</li>
                      <li><strong>Product Co-founder:</strong> User research, feature prioritization, and product roadmapping</li>
                      <li><strong>Technical Co-founder:</strong> Architecture design, technology selection, and implementation guidance</li>
                      <li><strong>Operations Co-founder:</strong> Legal setup, financial planning, and operational efficiency</li>
                      <li><strong>Marketing Co-founder:</strong> Brand strategy, customer acquisition, and growth tactics</li>
                    </ul>

                    <h3 id="livekit-integration">LiveKit Integration</h3>
                    <p>
                      For more interactive sessions, you can use our LiveKit integration to have real-time video or voice conversations with your AI co-founders:
                    </p>

                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 my-4 relative">
                      <button 
                        onClick={() => copyToClipboard(`import { liveKit } from 'ultimate-cofounder';

// Start a video session with specific agents
const session = await liveKit.createSession(
  ['strategic', 'product'], // agent IDs
  'video' // session type: 'video', 'voice', or 'screen-share'
);

// Connect to the room
await liveKit.connectToRoom(session);`)}
                        className="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                      >
                        {copiedCode === `import { liveKit } from 'ultimate-cofounder';

// Start a video session with specific agents
const session = await liveKit.createSession(
  ['strategic', 'product'], // agent IDs
  'video' // session type: 'video', 'voice', or 'screen-share'
);

// Connect to the room
await liveKit.connectToRoom(session);` ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                      </button>
                      <pre className="text-sm overflow-x-auto p-2 text-gray-800 dark:text-gray-200">
{`import { liveKit } from 'ultimate-cofounder';

// Start a video session with specific agents
const session = await liveKit.createSession(
  ['strategic', 'product'], // agent IDs
  'video' // session type: 'video', 'voice', or 'screen-share'
);

// Connect to the room
await liveKit.connectToRoom(session);`}
                      </pre>
                    </div>

                    <h3 id="bolt-integration">Bolt.new Integration</h3>
                    <p>
                      Our platform integrates with Bolt.new to help you quickly scaffold and deploy your startup's MVP:
                    </p>

                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 my-4 relative">
                      <button 
                        onClick={() => copyToClipboard(`import { boltIntegration } from 'ultimate-cofounder';

// Generate code scaffold from your idea
const files = await boltIntegration.generateScaffold(
  "Restaurant management app with reservation system",
  "React + Node.js + PostgreSQL"
);

// Create and deploy a project
const project = await boltIntegration.createProject(
  "restaurant-manager",
  "AI-powered restaurant management platform",
  files
);`)}
                        className="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                      >
                        {copiedCode === `import { boltIntegration } from 'ultimate-cofounder';

// Generate code scaffold from your idea
const files = await boltIntegration.generateScaffold(
  "Restaurant management app with reservation system",
  "React + Node.js + PostgreSQL"
);

// Create and deploy a project
const project = await boltIntegration.createProject(
  "restaurant-manager",
  "AI-powered restaurant management platform",
  files
);` ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                      </button>
                      <pre className="text-sm overflow-x-auto p-2 text-gray-800 dark:text-gray-200">
{`import { boltIntegration } from 'ultimate-cofounder';

// Generate code scaffold from your idea
const files = await boltIntegration.generateScaffold(
  "Restaurant management app with reservation system",
  "React + Node.js + PostgreSQL"
);

// Create and deploy a project
const project = await boltIntegration.createProject(
  "restaurant-manager",
  "AI-powered restaurant management platform",
  files
);`}
                      </pre>
                    </div>

                    <p>
                      For more detailed information, check out our <a href="#api" className="text-purple-600 dark:text-purple-400 hover:underline">API Reference</a> or <a href="#tutorials" className="text-purple-600 dark:text-purple-400 hover:underline">Video Tutorials</a>.
                    </p>
                  </div>
                </div>
              )}

              {activeTab === 'api' && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">API Reference</h2>
                  
                  <div className="prose dark:prose-invert max-w-none">
                    <p>
                      The Ultimate Co-founder API allows you to integrate our AI co-founder capabilities into your own applications and workflows.
                    </p>

                    <h3>Authentication</h3>
                    <p>
                      All API requests require authentication using a Bearer token:
                    </p>

                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 my-4 relative">
                      <button 
                        onClick={() => copyToClipboard(`curl -X POST https://api.ultimate-cofounder.com/v1/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"email": "<EMAIL>", "password": "your-password"}'`)}
                        className="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                      >
                        {copiedCode === `curl -X POST https://api.ultimate-cofounder.com/v1/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"email": "<EMAIL>", "password": "your-password"}'` ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                      </button>
                      <pre className="text-sm overflow-x-auto p-2 text-gray-800 dark:text-gray-200">
{`curl -X POST https://api.ultimate-cofounder.com/v1/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"email": "<EMAIL>", "password": "your-password"}'`}
                      </pre>
                    </div>

                    <h3>Endpoints</h3>
                    
                    <h4>Agents</h4>
                    <table className="min-w-full border border-gray-200 dark:border-gray-700 my-4">
                      <thead>
                        <tr className="bg-gray-50 dark:bg-gray-700">
                          <th className="px-4 py-2 text-left text-gray-700 dark:text-gray-300">Endpoint</th>
                          <th className="px-4 py-2 text-left text-gray-700 dark:text-gray-300">Method</th>
                          <th className="px-4 py-2 text-left text-gray-700 dark:text-gray-300">Description</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                        <tr>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">/api/v1/agents</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">GET</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">List all available agents</td>
                        </tr>
                        <tr>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">/api/v1/agents/execute</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">POST</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">Execute a task with a single agent</td>
                        </tr>
                        <tr>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">/api/v1/agents/execute-multi</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">POST</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">Execute a task with multiple agents</td>
                        </tr>
                      </tbody>
                    </table>

                    <h4>LiveKit Sessions</h4>
                    <table className="min-w-full border border-gray-200 dark:border-gray-700 my-4">
                      <thead>
                        <tr className="bg-gray-50 dark:bg-gray-700">
                          <th className="px-4 py-2 text-left text-gray-700 dark:text-gray-300">Endpoint</th>
                          <th className="px-4 py-2 text-left text-gray-700 dark:text-gray-300">Method</th>
                          <th className="px-4 py-2 text-left text-gray-700 dark:text-gray-300">Description</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                        <tr>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">/api/v1/livekit/sessions</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">POST</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">Create a new LiveKit session</td>
                        </tr>
                        <tr>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">/api/v1/livekit/sessions/{'{session_id}'}</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">GET</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">Get session details</td>
                        </tr>
                        <tr>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">/api/v1/livekit/sessions/{'{session_id}'}/end</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">POST</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">End a LiveKit session</td>
                        </tr>
                      </tbody>
                    </table>

                    <h4>Bolt.new Integration</h4>
                    <table className="min-w-full border border-gray-200 dark:border-gray-700 my-4">
                      <thead>
                        <tr className="bg-gray-50 dark:bg-gray-700">
                          <th className="px-4 py-2 text-left text-gray-700 dark:text-gray-300">Endpoint</th>
                          <th className="px-4 py-2 text-left text-gray-700 dark:text-gray-300">Method</th>
                          <th className="px-4 py-2 text-left text-gray-700 dark:text-gray-300">Description</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                        <tr>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">/api/v1/bolt/scaffold</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">POST</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">Generate code scaffold from prompt</td>
                        </tr>
                        <tr>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">/api/v1/bolt/projects</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">POST</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">Create a new Bolt.new project</td>
                        </tr>
                        <tr>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">/api/v1/bolt/projects/{'{project_id}'}/deploy</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">POST</td>
                          <td className="px-4 py-2 text-gray-900 dark:text-white">Deploy a Bolt.new project</td>
                        </tr>
                      </tbody>
                    </table>

                    <p>
                      For complete API documentation, download our <a href="#" className="text-purple-600 dark:text-purple-400 hover:underline flex items-center gap-1 inline-flex">OpenAPI specification <ExternalLink size={14} /></a>.
                    </p>
                  </div>
                </div>
              )}

              {activeTab === 'tutorials' && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Video Tutorials</h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                      <div className="aspect-video bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                        <Video className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                      </div>
                      <div className="p-4">
                        <h3 className="font-medium text-gray-900 dark:text-white mb-2">Getting Started with Ultimate Co-founder</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">Learn the basics of using the platform and communicating with AI agents.</p>
                        <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                          <span>10:23</span>
                          <span>•</span>
                          <span>June 25, 2025</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                      <div className="aspect-video bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                        <Video className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                      </div>
                      <div className="p-4">
                        <h3 className="font-medium text-gray-900 dark:text-white mb-2">LiveKit Video Sessions with AI Co-founders</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">How to conduct effective video sessions with your AI team.</p>
                        <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                          <span>15:47</span>
                          <span>•</span>
                          <span>June 28, 2025</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                      <div className="aspect-video bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                        <Video className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                      </div>
                      <div className="p-4">
                        <h3 className="font-medium text-gray-900 dark:text-white mb-2">Building an MVP with Technical Co-founder</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">Step-by-step guide to creating your first MVP with AI assistance.</p>
                        <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                          <span>20:12</span>
                          <span>•</span>
                          <span>July 2, 2025</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                      <div className="aspect-video bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                        <Video className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                      </div>
                      <div className="p-4">
                        <h3 className="font-medium text-gray-900 dark:text-white mb-2">Market Analysis with Strategic Co-founder</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">How to conduct comprehensive market research with AI assistance.</p>
                        <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                          <span>18:35</span>
                          <span>•</span>
                          <span>July 5, 2025</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'examples' && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Example Projects</h2>
                  
                  <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">Restaurant Management App</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        A complete example of building a restaurant management application with reservation system, menu management, and analytics dashboard.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-1">Strategic Analysis</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Market sizing and competitive landscape</p>
                        </div>
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-1">Product Roadmap</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Feature prioritization and user flows</p>
                        </div>
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-1">Technical Architecture</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">System design and implementation</p>
                        </div>
                      </div>
                      <a href="#" className="text-purple-600 dark:text-purple-400 hover:underline flex items-center gap-1">
                        View complete example <ExternalLink size={14} />
                      </a>
                    </div>
                    
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">SaaS Analytics Platform</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Building a B2B SaaS analytics platform from concept to launch with comprehensive guidance from all co-founders.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-1">Business Model</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Pricing strategy and revenue projections</p>
                        </div>
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-1">Operations Plan</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Legal setup and team structure</p>
                        </div>
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-1">Marketing Strategy</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Customer acquisition and growth tactics</p>
                        </div>
                      </div>
                      <a href="#" className="text-purple-600 dark:text-purple-400 hover:underline flex items-center gap-1">
                        View complete example <ExternalLink size={14} />
                      </a>
                    </div>
                    
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">E-commerce Marketplace</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Creating a two-sided marketplace with comprehensive planning and implementation guidance.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-1">Platform Architecture</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Scalable system design for marketplace</p>
                        </div>
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-1">User Acquisition</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Strategies for both sides of marketplace</p>
                        </div>
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                          <h4 className="font-medium text-gray-900 dark:text-white mb-1">Financial Projections</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Revenue model and unit economics</p>
                        </div>
                      </div>
                      <a href="#" className="text-purple-600 dark:text-purple-400 hover:underline flex items-center gap-1">
                        View complete example <ExternalLink size={14} />
                      </a>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};