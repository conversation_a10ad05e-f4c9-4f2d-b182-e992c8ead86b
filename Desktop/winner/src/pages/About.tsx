import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Zap, 
  ArrowLeft, 
  Users, 
  Award, 
  TrendingUp, 
  Calendar,
  CheckCircle,
  ExternalLink,
  ChevronRight,
  Heart,
  Rocket,
  Building2,
  Globe,
  Briefcase
} from 'lucide-react';

export const About: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">About Us</h1>
            </div>
            <div className="flex items-center gap-4">
              <Link to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <div className="bg-white dark:bg-gray-800 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">Our Story</h1>
              <p className="text-xl text-gray-600 dark:text-gray-400 mb-6">
                Ultimate Co-founder was born from a simple observation: great ideas often fail not because they're bad ideas, but because founders lack access to the right expertise at the right time.
              </p>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Our founders, Alex and Samantha, experienced this firsthand while building their previous startups. They realized that having specialized co-founders with expertise in strategy, product, technology, operations, and marketing was the key to success—but finding the perfect team is nearly impossible for most entrepreneurs.
              </p>
              <p className="text-gray-600 dark:text-gray-400">
                In 2024, they set out to solve this problem by creating AI co-founders that could provide specialized expertise to anyone with a great idea, regardless of their network or location. Today, Ultimate Co-founder is helping thousands of entrepreneurs turn their ideas into successful businesses.
              </p>
            </div>
            <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-8">
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mt-1">
                    <CheckCircle className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">Our Mission</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      To democratize entrepreneurship by providing everyone with access to world-class co-founder expertise through AI.
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mt-1">
                    <CheckCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">Our Vision</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      A world where anyone with a great idea can build a successful company with the help of AI co-founders.
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-1">
                    <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">Our Impact</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Over 10,000 founders supported, 500+ successful startups launched, and $50M+ in funding raised by our users.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Timeline Section */}
      <div className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12">Our Journey</h2>
          
          <div className="relative">
            {/* Vertical Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-0.5 bg-purple-200 dark:bg-purple-900/50"></div>
            
            <div className="space-y-12">
              {[
                {
                  year: '2023',
                  title: 'The Idea',
                  description: 'Alex and Samantha conceive the idea for Ultimate Co-founder after experiencing the challenges of finding the right expertise for their previous startups.',
                  icon: Lightbulb
                },
                {
                  year: '2024 Q1',
                  title: 'Founding & Seed Funding',
                  description: 'Ultimate Co-founder is officially founded and raises $3M in seed funding from top-tier investors to build the first version of the platform.',
                  icon: Rocket
                },
                {
                  year: '2024 Q2',
                  title: 'Beta Launch',
                  description: 'The platform launches in beta with the first three AI co-founders: Strategic, Product, and Technical. Over 500 founders join the waitlist in the first week.',
                  icon: Users
                },
                {
                  year: '2024 Q3',
                  title: 'Full Platform Launch',
                  description: 'All five AI co-founders are released, along with LiveKit integration for real-time video/voice collaboration and Bolt.new integration for code generation.',
                  icon: Zap
                },
                {
                  year: '2024 Q4',
                  title: 'Series A & Expansion',
                  description: 'The company raises a $12M Series A round and expands the team to 25 people across engineering, AI research, and customer success.',
                  icon: TrendingUp
                },
                {
                  year: '2025',
                  title: 'Global Expansion',
                  description: 'Ultimate Co-founder expands internationally, supporting founders in over 50 countries and integrating with 200+ third-party services via Composio.',
                  icon: Globe
                }
              ].map((milestone, index) => {
                const Milestone = milestone.icon;
                return (
                  <div key={index} className="relative flex items-center justify-center">
                    <div className={`absolute left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full bg-purple-600 flex items-center justify-center z-10`}>
                      <Milestone className="w-6 h-6 text-white" />
                    </div>
                    
                    <div className={`w-full md:w-5/12 ${index % 2 === 0 ? 'md:mr-auto md:text-right md:pr-16' : 'md:ml-auto md:pl-16'}`}>
                      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                        <div className="flex items-center gap-2 mb-3">
                          <Calendar className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                          <h3 className="text-lg font-bold text-gray-900 dark:text-white">{milestone.year}</h3>
                        </div>
                        <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-3">{milestone.title}</h4>
                        <p className="text-gray-600 dark:text-gray-400">{milestone.description}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Team Section */}
      <div className="py-20 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Meet Our Team</h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              We're a diverse team of AI researchers, engineers, and entrepreneurs passionate about democratizing startup success
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: 'Alex Chen',
                title: 'CEO & Co-founder',
                bio: 'Former Google AI lead with 15+ years experience in AI and entrepreneurship. Built and sold two AI startups.',
                image: 'https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg?auto=compress&cs=tinysrgb&w=300'
              },
              {
                name: 'Samantha Rodriguez',
                title: 'CTO & Co-founder',
                bio: 'Ex-OpenAI engineer specializing in multi-agent systems. PhD in Computer Science from Stanford.',
                image: 'https://images.pexels.com/photos/3796217/pexels-photo-3796217.jpeg?auto=compress&cs=tinysrgb&w=300'
              },
              {
                name: 'Michael Johnson',
                title: 'Chief Product Officer',
                bio: 'Former VP of Product at Figma. Passionate about creating intuitive user experiences for complex technologies.',
                image: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=300'
              },
              {
                name: 'Emily Wong',
                title: 'Chief AI Officer',
                bio: 'Leading expert in LLMs and multi-agent systems. Previously led AI research teams at Microsoft Research.',
                image: 'https://images.pexels.com/photos/3756679/pexels-photo-3756679.jpeg?auto=compress&cs=tinysrgb&w=300'
              },
              {
                name: 'David Patel',
                title: 'VP of Engineering',
                bio: 'Scaled engineering teams at multiple unicorn startups. Expert in building reliable, scalable AI systems.',
                image: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=300'
              },
              {
                name: 'Sarah Kim',
                title: 'Head of Operations',
                bio: 'Former COO at a Y Combinator startup. Specializes in operational excellence and scaling startups.',
                image: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=300'
              }
            ].map((member, index) => (
              <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden shadow-sm">
                <img 
                  src={member.image} 
                  alt={member.name} 
                  className="w-full h-64 object-cover object-center"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1">{member.name}</h3>
                  <p className="text-purple-600 dark:text-purple-400 font-medium mb-3">{member.title}</p>
                  <p className="text-gray-600 dark:text-gray-400">{member.bio}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link 
              to="/careers"
              className="inline-flex items-center gap-2 text-purple-600 dark:text-purple-400 hover:underline"
            >
              <Briefcase className="w-4 h-4" />
              Join our team - View open positions
              <ChevronRight className="w-4 h-4" />
            </Link>
          </div>
        </div>
      </div>

      {/* Values Section */}
      <div className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Our Core Values</h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              The principles that guide everything we do at Ultimate Co-founder
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: Users,
                title: 'Founder First',
                description: 'We put founders at the center of everything we do, designing our platform to empower their vision and amplify their capabilities.'
              },
              {
                icon: TrendingUp,
                title: 'Excellence Through AI',
                description: "We're committed to building AI co-founders that provide world-class expertise and guidance across all business domains."
              },
              {
                icon: Globe,
                title: 'Global Access',
                description: 'We believe entrepreneurship should be accessible to everyone, regardless of location, background, or network.'
              },
              {
                icon: Award,
                title: 'Continuous Innovation',
                description: "We constantly push the boundaries of what's possible with AI to provide ever-improving co-founder experiences."
              },
              {
                icon: Heart,
                title: 'Ethical AI',
                description: "We develop our AI co-founders with strong ethical principles, ensuring they provide guidance that's both effective and responsible."
              },
              {
                icon: Building2,
                title: 'Long-term Partnership',
                description: "We're committed to supporting founders throughout their entire journey, from idea to exit."
              }
            ].map((value, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mb-4">
                  <value.icon className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">{value.title}</h3>
                <p className="text-gray-600 dark:text-gray-400">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Investors Section */}
      <div className="py-20 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Backed by the Best</h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              We're proud to be supported by leading investors who share our vision
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
              <div key={i} className="flex items-center justify-center p-6 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="w-24 h-12 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center">
                  <span className="text-gray-400 dark:text-gray-500 font-medium">Investor {i}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-6">Join the Future of Entrepreneurship</h2>
          <p className="text-xl text-purple-100 mb-8 max-w-3xl mx-auto">
            Experience the power of AI co-founders and start building your startup today
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <button className="px-8 py-4 bg-white text-purple-600 rounded-xl font-semibold text-lg transition-all hover:bg-gray-100">
              Start Free Trial
            </button>
            <Link 
              to="/contact"
              className="px-8 py-4 bg-purple-700 text-white rounded-xl font-semibold text-lg transition-all hover:bg-purple-800"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

// Custom Lightbulb icon
const Lightbulb: React.FC<{ className?: string }> = ({ className }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <path d="M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5"></path>
    <path d="M9 18h6"></path>
    <path d="M10 22h4"></path>
  </svg>
);