import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  Zap, 
  ArrowLeft, 
  Search, 
  Plus, 
  CheckCircle, 
  AlertCircle,
  Settings,
  ExternalLink,
  RefreshCw,
  Slack,
  Github,
  FileText,
  Mail,
  Calendar,
  Database,
  CreditCard,
  BarChart3,
  Phone,
  MessageSquare,
  Video,
  X
} from 'lucide-react';

interface Integration {
  id: string;
  name: string;
  description: string;
  category: 'Communication' | 'Development' | 'Productivity' | 'Storage' | 'Analytics' | 'Finance' | 'CRM';
  icon: React.ElementType;
  status: 'connected' | 'disconnected';
  lastSync?: string;
  features: string[];
}

export const Integrations: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showConnectModal, setShowConnectModal] = useState(false);
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null);

  // Mock integrations data
  const integrations: Integration[] = [
    {
      id: 'slack',
      name: 'Slack',
      description: 'Team communication and notifications',
      category: 'Communication',
      icon: Slack,
      status: 'connected',
      lastSync: '2025-06-28T14:30:00Z',
      features: ['Send notifications', 'Create channels', 'Post messages', 'Manage users']
    },
    {
      id: 'github',
      name: 'GitHub',
      description: 'Code repository and version control',
      category: 'Development',
      icon: Github,
      status: 'connected',
      lastSync: '2025-06-27T10:15:00Z',
      features: ['Create repositories', 'Manage issues', 'Pull requests', 'Code reviews']
    },
    {
      id: 'notion',
      name: 'Notion',
      description: 'Documentation and knowledge base',
      category: 'Productivity',
      icon: FileText,
      status: 'disconnected',
      features: ['Create pages', 'Manage databases', 'Team wikis', 'Project tracking']
    },
    {
      id: 'google-drive',
      name: 'Google Drive',
      description: 'File storage and collaboration',
      category: 'Storage',
      icon: Database,
      status: 'connected',
      lastSync: '2025-06-26T16:45:00Z',
      features: ['File storage', 'Document sharing', 'Collaborative editing', 'Version history']
    },
    {
      id: 'gmail',
      name: 'Gmail',
      description: 'Email communication',
      category: 'Communication',
      icon: Mail,
      status: 'disconnected',
      features: ['Send emails', 'Read inbox', 'Manage drafts', 'Email templates']
    },
    {
      id: 'google-calendar',
      name: 'Google Calendar',
      description: 'Schedule management',
      category: 'Productivity',
      icon: Calendar,
      status: 'disconnected',
      features: ['Create events', 'Manage schedules', 'Set reminders', 'Team calendars']
    },
    {
      id: 'stripe',
      name: 'Stripe',
      description: 'Payment processing',
      category: 'Finance',
      icon: CreditCard,
      status: 'disconnected',
      features: ['Process payments', 'Manage subscriptions', 'Invoice customers', 'Financial reporting']
    },
    {
      id: 'google-analytics',
      name: 'Google Analytics',
      description: 'Website analytics',
      category: 'Analytics',
      icon: BarChart3,
      status: 'disconnected',
      features: ['Track visitors', 'Analyze behavior', 'Conversion tracking', 'Custom reports']
    },
    {
      id: 'twilio',
      name: 'Twilio',
      description: 'SMS and voice communication',
      category: 'Communication',
      icon: Phone,
      status: 'disconnected',
      features: ['Send SMS', 'Voice calls', 'Verification codes', 'Automated messages']
    },
    {
      id: 'discord',
      name: 'Discord',
      description: 'Community chat platform',
      category: 'Communication',
      icon: MessageSquare,
      status: 'disconnected',
      features: ['Server management', 'Channel creation', 'Bot integration', 'Role management']
    },
    {
      id: 'zoom',
      name: 'Zoom',
      description: 'Video conferencing',
      category: 'Communication',
      icon: Video,
      status: 'disconnected',
      features: ['Schedule meetings', 'Join calls', 'Webinar hosting', 'Recording management']
    }
  ];

  const categories = Array.from(new Set(integrations.map(i => i.category)));

  const filteredIntegrations = integrations
    .filter(integration => 
      integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      integration.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .filter(integration => 
      !selectedCategory || integration.category === selectedCategory
    );

  const connectedIntegrations = filteredIntegrations.filter(i => i.status === 'connected');
  const availableIntegrations = filteredIntegrations.filter(i => i.status === 'disconnected');

  const handleConnectIntegration = (integration: Integration) => {
    setSelectedIntegration(integration);
    setShowConnectModal(true);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Communication':
        return <MessageSquare className="w-4 h-4" />;
      case 'Development':
        return <Github className="w-4 h-4" />;
      case 'Productivity':
        return <Calendar className="w-4 h-4" />;
      case 'Storage':
        return <Database className="w-4 h-4" />;
      case 'Analytics':
        return <BarChart3 className="w-4 h-4" />;
      case 'Finance':
        return <CreditCard className="w-4 h-4" />;
      case 'CRM':
        return <Users className="w-4 h-4" />;
      default:
        return <Zap className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">Integrations</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search integrations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-64 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white"
                />
              </div>
              <Link to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Integrations Overview */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Integrations</h2>
              <p className="text-gray-600 dark:text-gray-400">
                Connect your favorite tools and services to enhance your co-founder experience
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                <Plus className="w-4 h-4" />
                <span>Add Integration</span>
              </button>
              <button className="p-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                <RefreshCw className="w-4 h-4" />
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center">
                <Zap className="w-6 h-6 text-purple-600 dark:text-purple-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">{integrations.length}</p>
                <p className="text-sm text-purple-600 dark:text-purple-400">Available Integrations</p>
              </div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">{integrations.filter(i => i.status === 'connected').length}</p>
                <p className="text-sm text-green-600 dark:text-green-400">Connected</p>
              </div>
            </div>
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                <Settings className="w-6 h-6 text-blue-600 dark:text-blue-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{categories.length}</p>
                <p className="text-sm text-blue-600 dark:text-blue-400">Categories</p>
              </div>
            </div>
          </div>

          {/* Category Filters */}
          <div className="mb-8">
            <h3 className="font-bold text-gray-900 dark:text-white mb-4">Categories</h3>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setSelectedCategory(null)}
                className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === null
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                All
              </button>
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                    selectedCategory === category
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {getCategoryIcon(category)}
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Connected Integrations */}
          {connectedIntegrations.length > 0 && (
            <div className="mb-8">
              <h3 className="font-bold text-gray-900 dark:text-white mb-4">Connected Integrations</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {connectedIntegrations.map((integration) => {
                  const IconComponent = integration.icon;
                  return (
                    <div
                      key={integration.id}
                      className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border-2 border-green-500"
                    >
                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center">
                          <IconComponent className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="font-medium text-gray-900 dark:text-white">{integration.name}</h4>
                            <span className="flex items-center gap-1 text-green-600 dark:text-green-400 text-xs">
                              <CheckCircle className="w-3 h-3" />
                              Connected
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{integration.description}</p>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-gray-500 dark:text-gray-400">
                              Last sync: {new Date(integration.lastSync!).toLocaleString()}
                            </span>
                            <div className="flex items-center gap-2">
                              <button className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                                <RefreshCw className="w-3 h-3" />
                              </button>
                              <button className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                                <Settings className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Available Integrations */}
          <div>
            <h3 className="font-bold text-gray-900 dark:text-white mb-4">Available Integrations</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {availableIntegrations.map((integration) => {
                const IconComponent = integration.icon;
                return (
                  <div
                    key={integration.id}
                    className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center">
                        <IconComponent className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="font-medium text-gray-900 dark:text-white">{integration.name}</h4>
                          <span className="px-2 py-0.5 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-xs">
                            {integration.category}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{integration.description}</p>
                        <button
                          onClick={() => handleConnectIntegration(integration)}
                          className="w-full flex items-center justify-center gap-2 px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors"
                        >
                          <Plus className="w-3 h-3" />
                          Connect
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Connect Integration Modal */}
      {showConnectModal && selectedIntegration && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    {React.createElement(selectedIntegration.icon, { className: "w-6 h-6 text-gray-700 dark:text-gray-300" })}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">Connect {selectedIntegration.name}</h3>
                </div>
                <button 
                  onClick={() => setShowConnectModal(false)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Connect your {selectedIntegration.name} account to enable these features:
              </p>

              <div className="space-y-2 mb-6">
                {selectedIntegration.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => {
                    // Simulate connection
                    setTimeout(() => {
                      setShowConnectModal(false);
                    }, 1000);
                  }}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  <ExternalLink className="w-4 h-4" />
                  Connect {selectedIntegration.name}
                </button>
                <button
                  onClick={() => setShowConnectModal(false)}
                  className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};