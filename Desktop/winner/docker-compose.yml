version: '3.8'

services:
  # Frontend (React + Vite)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "5173:5173"
    environment:
      - VITE_API_URL=http://backend:8000
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - backend
    command: npm run dev

  # Backend (FastAPI + Python)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/ultimate_cofounder
      - REDIS_URL=redis://redis:6379
      - FRONTEND_URL=http://localhost:5173
    env_file:
      - ./backend/.env
    depends_on:
      - db
      - redis
    volumes:
      - ./backend:/app
    command: python run.py

  # PostgreSQL Database
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=ultimate_cofounder
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  # Nginx reverse proxy (production)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    profiles:
      - production

volumes:
  postgres_data:
  redis_data: