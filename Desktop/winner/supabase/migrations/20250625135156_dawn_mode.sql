/*
  # Ultimate Co-founder Database Schema

  1. New Tables
    - `users` - User accounts with authentication
    - `agents` - AI co-founder agent definitions
    - `sessions` - LiveKit video/voice sessions
    - `tasks` - Agent tasks and execution tracking
    - `integrations` - Third-party service connections

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
    - Create indexes for performance

  3. Initial Data
    - Demo user account
    - Default AI agent configurations
*/

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    email text UNIQUE NOT NULL,
    name text NOT NULL,
    hashed_password text NOT NULL,
    is_active boolean DEFAULT true,
    is_verified boolean DEFAULT false,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create agents table
CREATE TABLE IF NOT EXISTS agents (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id text UNIQUE NOT NULL,
    name text NOT NULL,
    role text NOT NULL,
    status text DEFAULT 'active',
    description text,
    capabilities jsonb DEFAULT '[]'::jsonb,
    config jsonb DEFAULT '{}'::jsonb,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create sessions table
CREATE TABLE IF NOT EXISTS sessions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES users(id) ON DELETE CASCADE,
    session_type text NOT NULL,
    status text DEFAULT 'pending',
    room_name text,
    livekit_token text,
    agent_ids jsonb DEFAULT '[]'::jsonb,
    duration integer DEFAULT 0,
    recording_url text,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create tasks table
CREATE TABLE IF NOT EXISTS tasks (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES users(id) ON DELETE CASCADE,
    session_id uuid REFERENCES sessions(id) ON DELETE SET NULL,
    agent_id text NOT NULL,
    task_type text NOT NULL,
    description text NOT NULL,
    status text DEFAULT 'pending',
    input_data jsonb DEFAULT '{}'::jsonb,
    result_data jsonb DEFAULT '{}'::jsonb,
    execution_time integer DEFAULT 0,
    error_message text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create integrations table
CREATE TABLE IF NOT EXISTS integrations (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES users(id) ON DELETE CASCADE,
    integration_type text NOT NULL,
    name text NOT NULL,
    status text DEFAULT 'disconnected',
    config jsonb DEFAULT '{}'::jsonb,
    credentials jsonb DEFAULT '{}'::jsonb,
    last_sync timestamptz,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create projects table
CREATE TABLE IF NOT EXISTS projects (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES users(id) ON DELETE CASCADE,
    name text NOT NULL,
    description text,
    status text DEFAULT 'planning',
    config jsonb DEFAULT '{}'::jsonb,
    agent_outputs jsonb DEFAULT '{}'::jsonb,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for users table
CREATE POLICY "Users can read own data"
    ON users FOR SELECT
    TO authenticated
    USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own data"
    ON users FOR UPDATE
    TO authenticated
    USING (auth.uid()::text = id::text);

-- Create RLS policies for agents table (public read)
CREATE POLICY "Anyone can read agents"
    ON agents FOR SELECT
    TO authenticated
    USING (true);

-- Create RLS policies for sessions table
CREATE POLICY "Users can read own sessions"
    ON sessions FOR SELECT
    TO authenticated
    USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can create own sessions"
    ON sessions FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own sessions"
    ON sessions FOR UPDATE
    TO authenticated
    USING (auth.uid()::text = user_id::text);

-- Create RLS policies for tasks table
CREATE POLICY "Users can read own tasks"
    ON tasks FOR SELECT
    TO authenticated
    USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can create own tasks"
    ON tasks FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own tasks"
    ON tasks FOR UPDATE
    TO authenticated
    USING (auth.uid()::text = user_id::text);

-- Create RLS policies for integrations table
CREATE POLICY "Users can read own integrations"
    ON integrations FOR SELECT
    TO authenticated
    USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can create own integrations"
    ON integrations FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own integrations"
    ON integrations FOR UPDATE
    TO authenticated
    USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can delete own integrations"
    ON integrations FOR DELETE
    TO authenticated
    USING (auth.uid()::text = user_id::text);

-- Create RLS policies for projects table
CREATE POLICY "Users can read own projects"
    ON projects FOR SELECT
    TO authenticated
    USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can create own projects"
    ON projects FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own projects"
    ON projects FOR UPDATE
    TO authenticated
    USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can delete own projects"
    ON projects FOR DELETE
    TO authenticated
    USING (auth.uid()::text = user_id::text);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

CREATE INDEX IF NOT EXISTS idx_agents_agent_id ON agents(agent_id);
CREATE INDEX IF NOT EXISTS idx_agents_status ON agents(status);

CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status);
CREATE INDEX IF NOT EXISTS idx_sessions_created_at ON sessions(created_at);

CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_tasks_agent_id ON tasks(agent_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);

CREATE INDEX IF NOT EXISTS idx_integrations_user_id ON integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_integrations_type ON integrations(integration_type);
CREATE INDEX IF NOT EXISTS idx_integrations_status ON integrations(status);

CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);

-- Insert default AI agent configurations
INSERT INTO agents (agent_id, name, role, status, description, capabilities, config)
VALUES 
    (
        'strategic',
        'Alex Strategic',
        'Strategic Co-founder',
        'active',
        'Strategic planning, market analysis, and business direction',
        '["Market Research", "Strategic Planning", "Competitive Analysis", "Business Modeling"]'::jsonb,
        '{"avatar": "🎯", "color": "from-blue-500 to-purple-600"}'::jsonb
    ),
    (
        'product',
        'Sam Product',
        'Product Co-founder',
        'active',
        'Product strategy, user experience, and feature prioritization',
        '["Product Strategy", "UX Design", "Feature Planning", "User Research"]'::jsonb,
        '{"avatar": "🚀", "color": "from-purple-500 to-pink-600"}'::jsonb
    ),
    (
        'technical',
        'Taylor Tech',
        'Technical Co-founder',
        'active',
        'Architecture, development, and technical implementation',
        '["System Architecture", "Code Generation", "Technical Review", "DevOps"]'::jsonb,
        '{"avatar": "⚡", "color": "from-green-500 to-blue-600"}'::jsonb
    ),
    (
        'operations',
        'Jordan Ops',
        'Operations Co-founder',
        'active',
        'Process optimization, resource management, and operations',
        '["Process Design", "Resource Planning", "Quality Assurance", "Automation"]'::jsonb,
        '{"avatar": "⚙️", "color": "from-orange-500 to-red-600"}'::jsonb
    ),
    (
        'marketing',
        'Morgan Marketing',
        'Marketing Co-founder',
        'active',
        'Growth strategy, brand building, and customer acquisition',
        '["Growth Strategy", "Content Content", "Analytics", "Campaign Management"]'::jsonb,
        '{"avatar": "📈", "color": "from-pink-500 to-red-600"}'::jsonb
    )
ON CONFLICT (agent_id) DO NOTHING;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sessions_updated_at BEFORE UPDATE ON sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_integrations_updated_at BEFORE UPDATE ON integrations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();