from fastapi import WebSocket
from typing import Dict, List
import json
import asyncio

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.client_data: Dict[str, Dict] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """Connect a new WebSocket client"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.client_data[client_id] = {
            "connected_at": "2025-06-25T13:30:00Z",
            "status": "connected"
        }
        
        # Send welcome message
        await self.send_personal_message(json.dumps({
            "type": "connection",
            "message": "Connected to Ultimate Co-founder WebSocket",
            "client_id": client_id
        }), client_id)
    
    def disconnect(self, client_id: str):
        """Disconnect a WebSocket client"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        if client_id in self.client_data:
            del self.client_data[client_id]
    
    async def send_personal_message(self, message: str, client_id: str):
        """Send message to specific client"""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(message)
            except Exception as e:
                print(f"Error sending message to {client_id}: {e}")
                self.disconnect(client_id)
    
    async def broadcast(self, message: str):
        """Broadcast message to all connected clients"""
        disconnected_clients = []
        
        for client_id, connection in self.active_connections.items():
            try:
                await connection.send_text(message)
            except Exception as e:
                print(f"Error broadcasting to {client_id}: {e}")
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    async def send_agent_update(self, client_id: str, agent_id: str, status: str, message: str = None):
        """Send agent status update to client"""
        update = {
            "type": "agent_update",
            "agent_id": agent_id,
            "status": status,
            "message": message,
            "timestamp": "2025-06-25T13:30:00Z"
        }
        
        await self.send_personal_message(json.dumps(update), client_id)
    
    async def send_task_progress(self, client_id: str, task_id: str, progress: int, message: str = None):
        """Send task progress update to client"""
        update = {
            "type": "task_progress",
            "task_id": task_id,
            "progress": progress,
            "message": message,
            "timestamp": "2025-06-25T13:30:00Z"
        }
        
        await self.send_personal_message(json.dumps(update), client_id)
    
    def get_connected_clients(self) -> List[str]:
        """Get list of connected client IDs"""
        return list(self.active_connections.keys())